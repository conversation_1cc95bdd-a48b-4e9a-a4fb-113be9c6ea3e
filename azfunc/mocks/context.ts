import { Context } from '@azure/functions';

interface MockLogger extends jest.Mock {
  error: jest.<PERSON><PERSON>,
  warn: jest.Mock,
  info: jest.<PERSON><PERSON>,
  verbose: jest.Mock,
}

export const mockLogger = jest.fn().mockReturnThis() as MockLogger;
mockLogger.error = mockLogger;
mockLogger.warn = mockLogger;
mockLogger.info = mockLogger;
mockLogger.verbose = mockLogger;

export function createMockContext(bindings?: any) {
  return {
    log: mockLogger,
    ...bindings,
  } as unknown as Context;
}
