const HttpStatusCode = {
    /** 
     * 404 Not Found
     */ 
    NotFound: 404,
    /**
     * 409 Conflict
     */
    Conflict: 409,
    /** 
     * 412 Precondition Failed
     */
    PreconditionFailed: 412,
    /** 
     * 429 Too Many Requests
     */
    TooManyRequests: 429,
    /**
     * 503 Unavailable
     */
    Unavailable: 503,
    /** 
     * 504 Gateway Timeout
     */
    GatewayTimeout: 504,
    /** 
     * 403 Forbidden
     */
    Forbidden: 403,
    
    isRecoverable: (statusCode: number | undefined): boolean => {
      switch (statusCode) {
        case HttpStatusCode.Conflict:
        case HttpStatusCode.PreconditionFailed:
        case HttpStatusCode.TooManyRequests:
        case HttpStatusCode.Unavailable:
        case HttpStatusCode.GatewayTimeout:
          return true;
        default:
          return false;
      }
    }
  };

export default HttpStatusCode;