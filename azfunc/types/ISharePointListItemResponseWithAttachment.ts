import { IAttachmentResponse } from './IAttachmentResponse';
import { ICompanyNewsCustomColumns } from './ICompanyNewsCustomColumns';

/**
 * 添付ファイル付きSharePoint項目詳細のレスポンスデータ
 * _api/web/Lists('list-GUID')/GetItemByUniqueId('item-GUID')
 * ?$expand=AttachmentFiles
 */
export interface ISharePointListItemResponseWithAttachment extends ICompanyNewsCustomColumns {
  'odata.metadata'?: string | null;
  'odata.type'?: string | null;
  'odata.id'?: string | null;
  'odata.etag'?: string | null;
  'odata.editLink'?: string | null;

  // リスト項目
  FileSystemObjectType?: number | null;
  Id?: number | null;
  GUID?: string | null;
  ServerRedirectedEmbedUri?: string | null;
  ServerRedirectedEmbedUrl?: string | null;
  Title?: string | null;
  ContentTypeId?: string | null;
  Modified?: string | null;
  Created?: string | null;
  AuthorId?: number | null
  EditorId?: number | null

  // 添付ファイル
  '<EMAIL>'?: string | null;
  AttachmentFiles?: IAttachmentResponse[];
}
