import { Logger } from "@azure/functions";
import Dictionary from "./Dictionary";
import ISearchContext from "./ISearchContext";

export type ISearchConditionTree = string | {
  operator: 'And' | 'Or',
  operand: ISearchConditionTree[],
};

export default interface ISearchParameters {
    conditionKeywords: ISearchConditionTree,
    res: Dictionary<string, string>,
    properties: {
        global: Dictionary<string, string>,
        local: ISearchContext[],
    },
    logger?: Logger,
};