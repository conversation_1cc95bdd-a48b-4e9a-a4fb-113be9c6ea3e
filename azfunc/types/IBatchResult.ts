import Dictionary from "./Dictionary";

export default interface IBatchResult {
  /**
   * データソースの属性
   */
  properties: Dictionary<string, string>,
  /**
   * データ取得に成功したかどうか
   */
  success: boolean,

  /** 
   * 現在のページ番号
   */
  page: number,

  /**
   * データ取得の際に発生したエラー
   */
  error?: any,
  
  /** 
   * 次ページが存在するか
   */
  hasNext?: boolean,

  /**
   * 取得したID
   */
  ids?: string[],

  /** 
   * 次ページを取得する際に用いるコンテキスト
   */
  pagingContext?: Dictionary<string, string>,

  /**
   * Table Storageに登録する際のキー
   */
  uniqueKey?: string,
};
