import { Client, FetchOptions } from '@microsoft/microsoft-graph-client';
import { TokenProvider } from "../types/TokenProvider";


/**
 * SharePoint通信用のクライアントを生成する
 * @param tokenProvider
 * @param baseUrl
 * @param customHost
 */
export function createSpClient(
  tokenProvider: TokenProvider,
  baseUrl: string,
  customHost: string
): Client | undefined {
  return Client.initWithMiddleware({
    baseUrl,
    customHosts: new Set<string>([customHost]),
    defaultVersion: '_api',
    fetchOptions: {
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    } as unknown as FetchOptions,
    authProvider: { getAccessToken: tokenProvider},
    //middleware: authHandler,
  });
}

