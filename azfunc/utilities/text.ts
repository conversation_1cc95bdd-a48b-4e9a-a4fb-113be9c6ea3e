import { isSharedLinkCommand } from './regexp';

export const EN_DASH = '–';

/**
 * 文字列が空のときはダーシを出力する
 *
 * @param {(string | null | undefined)} text
 * @return {*}  {string}
 */
export function blankToDash(text: string | null | undefined): string {
  if (!text || text === '') {
    return EN_DASH;
  }
  return text;
}

/**
 * 前後にスラッシュが存在する場合はトリムして返却する
 *
 * @param {(string | null | undefined)} text
 * @return {*}  {string}
 */
export function trimPreSufSlash(text: string | null | undefined): string {
  if (!text || text === '') return '';
  return text
    .replace(/^\/+/, '')
    .replace(/\/+$/, '');
}

/**
 * テンプレートリテラルを出力する
 * @example printTemplate`お気に入り ${count}件`
 * @param hashes
 * @param values
 */
export function printTemplate(hashes: TemplateStringsArray, ...values: string[]): string {
  return values
    .map((value, i) => hashes[i] + value)
    .concat(hashes.slice(values.length))
    .join('');
}

/**
 * SharePoint RESTのURL形式に沿った形でエンコードする
 * https://sharepoint.stackexchange.com/questions/241015/rest-filtering-a-value-containing-apostrophe
 *
 * @param value
 */
export function fixedEncodeURIComponent(value: string | number | boolean): string {
  return encodeURIComponent(value).replace(/[']/g, '%27%27');
}

/**
 * 全角英数を半角英数に変換する
 * @param str
 */
export function fullCaseToHalfCase(str: string): string {
  return str.replace(/[！-～]/g, (s) => String.fromCharCode(s.charCodeAt(0) - 0xFEE0));
}

/**
 * 半角英数を全角英数にそのまま変換する
 * @param str
 */
export function halfCaseToFullCaseAsIs(str: string): string {
  return str.replace(/[[!-~]/g, (s) => String.fromCharCode(s.charCodeAt(0) + 0xFEE0));
}

/**
 * 半角カタカナを全角カタカナに変換する
 * @param str
 */
export function halfKataToFullKata(str: string): string {
  /* eslint-disable quote-props */
  const kanaMap: { [k: string]: string } = {
    'ｶﾞ': 'ガ',
    'ｷﾞ': 'ギ',
    'ｸﾞ': 'グ',
    'ｹﾞ': 'ゲ',
    'ｺﾞ': 'ゴ',
    'ｻﾞ': 'ザ',
    'ｼﾞ': 'ジ',
    'ｽﾞ': 'ズ',
    'ｾﾞ': 'ゼ',
    'ｿﾞ': 'ゾ',
    'ﾀﾞ': 'ダ',
    'ﾁﾞ': 'ヂ',
    'ﾂﾞ': 'ヅ',
    'ﾃﾞ': 'デ',
    'ﾄﾞ': 'ド',
    'ﾊﾞ': 'バ',
    'ﾋﾞ': 'ビ',
    'ﾌﾞ': 'ブ',
    'ﾍﾞ': 'ベ',
    'ﾎﾞ': 'ボ',
    'ﾊﾟ': 'パ',
    'ﾋﾟ': 'ピ',
    'ﾌﾟ': 'プ',
    'ﾍﾟ': 'ペ',
    'ﾎﾟ': 'ポ',
    'ｳﾞ': 'ヴ',
    'ﾜﾞ': 'ヷ',
    'ｦﾞ': 'ヺ',
    'ｱ': 'ア',
    'ｲ': 'イ',
    'ｳ': 'ウ',
    'ｴ': 'エ',
    'ｵ': 'オ',
    'ｶ': 'カ',
    'ｷ': 'キ',
    'ｸ': 'ク',
    'ｹ': 'ケ',
    'ｺ': 'コ',
    'ｻ': 'サ',
    'ｼ': 'シ',
    'ｽ': 'ス',
    'ｾ': 'セ',
    'ｿ': 'ソ',
    'ﾀ': 'タ',
    'ﾁ': 'チ',
    'ﾂ': 'ツ',
    'ﾃ': 'テ',
    'ﾄ': 'ト',
    'ﾅ': 'ナ',
    'ﾆ': 'ニ',
    'ﾇ': 'ヌ',
    'ﾈ': 'ネ',
    'ﾉ': 'ノ',
    'ﾊ': 'ハ',
    'ﾋ': 'ヒ',
    'ﾌ': 'フ',
    'ﾍ': 'ヘ',
    'ﾎ': 'ホ',
    'ﾏ': 'マ',
    'ﾐ': 'ミ',
    'ﾑ': 'ム',
    'ﾒ': 'メ',
    'ﾓ': 'モ',
    'ﾔ': 'ヤ',
    'ﾕ': 'ユ',
    'ﾖ': 'ヨ',
    'ﾗ': 'ラ',
    'ﾘ': 'リ',
    'ﾙ': 'ル',
    'ﾚ': 'レ',
    'ﾛ': 'ロ',
    'ﾜ': 'ワ',
    'ｦ': 'ヲ',
    'ﾝ': 'ン',
    'ｧ': 'ァ',
    'ｨ': 'ィ',
    'ｩ': 'ゥ',
    'ｪ': 'ェ',
    'ｫ': 'ォ',
    'ｯ': 'ッ',
    'ｬ': 'ャ',
    'ｭ': 'ュ',
    'ｮ': 'ョ',
    '｡': '。',
    '､': '、',
    'ｰ': 'ー',
    '｢': '「',
    '｣': '」',
    '･': '・',
  };

  const reg = new RegExp(`(${Object.keys(kanaMap).join('|')})`, 'g');
  return str
    .replace(reg, (match) => kanaMap[match])
    .replace(/ﾞ/g, '゛')
    .replace(/ﾟ/g, '゜');
}

/**
 * 全角カタカナを半角カタカナに変換する
 * @param str
 */
export function fullKataToHalfKata(str: string): string {
  const kanaMap: { [k: string]: string } = {
    'ガ': 'ｶﾞ',
    'ギ': 'ｷﾞ',
    'グ': 'ｸﾞ',
    'ゲ': 'ｹﾞ',
    'ゴ': 'ｺﾞ',
    'ザ': 'ｻﾞ',
    'ジ': 'ｼﾞ',
    'ズ': 'ｽﾞ',
    'ゼ': 'ｾﾞ',
    'ゾ': 'ｿﾞ',
    'ダ': 'ﾀﾞ',
    'ヂ': 'ﾁﾞ',
    'ヅ': 'ﾂﾞ',
    'デ': 'ﾃﾞ',
    'ド': 'ﾄﾞ',
    'バ': 'ﾊﾞ',
    'ビ': 'ﾋﾞ',
    'ブ': 'ﾌﾞ',
    'ベ': 'ﾍﾞ',
    'ボ': 'ﾎﾞ',
    'パ': 'ﾊﾟ',
    'ピ': 'ﾋﾟ',
    'プ': 'ﾌﾟ',
    'ペ': 'ﾍﾟ',
    'ポ': 'ﾎﾟ',
    'ヴ': 'ｳﾞ',
    'ヷ': 'ﾜﾞ',
    'ヺ': 'ｦﾞ',
    'ア': 'ｱ',
    'イ': 'ｲ',
    'ウ': 'ｳ',
    'エ': 'ｴ',
    'オ': 'ｵ',
    'カ': 'ｶ',
    'キ': 'ｷ',
    'ク': 'ｸ',
    'ケ': 'ｹ',
    'コ': 'ｺ',
    'サ': 'ｻ',
    'シ': 'ｼ',
    'ス': 'ｽ',
    'セ': 'ｾ',
    'ソ': 'ｿ',
    'タ': 'ﾀ',
    'チ': 'ﾁ',
    'ツ': 'ﾂ',
    'テ': 'ﾃ',
    'ト': 'ﾄ',
    'ナ': 'ﾅ',
    'ニ': 'ﾆ',
    'ヌ': 'ﾇ',
    'ネ': 'ﾈ',
    'ノ': 'ﾉ',
    'ハ': 'ﾊ',
    'ヒ': 'ﾋ',
    'フ': 'ﾌ',
    'ヘ': 'ﾍ',
    'ホ': 'ﾎ',
    'マ': 'ﾏ',
    'ミ': 'ﾐ',
    'ム': 'ﾑ',
    'メ': 'ﾒ',
    'モ': 'ﾓ',
    'ヤ': 'ﾔ',
    'ユ': 'ﾕ',
    'ヨ': 'ﾖ',
    'ラ': 'ﾗ',
    'リ': 'ﾘ',
    'ル': 'ﾙ',
    'レ': 'ﾚ',
    'ロ': 'ﾛ',
    'ワ': 'ﾜ',
    'ヲ': 'ｦ',
    'ン': 'ﾝ',
    'ァ': 'ｧ',
    'ィ': 'ｨ',
    'ゥ': 'ｩ',
    'ェ': 'ｪ',
    'ォ': 'ｫ',
    'ッ': 'ｯ',
    'ャ': 'ｬ',
    'ュ': 'ｭ',
    'ョ': 'ｮ',
    '。': '｡',
    '、': '､',
    'ー': 'ｰ',
    '「': '｢',
    '」': '｣',
    '・': '･',
  };
  const reg = new RegExp(`(${Object.keys(kanaMap).join('|')})`, 'g');
  return str
    .replace(reg, (match) => kanaMap[match])
    .replace(/゛/g, 'ﾞ')
    .replace(/゜/g, 'ﾟ');
}

/**
 * \:i:\r\ で始まる文字列を \ 開始に置換する。末尾の?パラメータを除去する
 * @param path
 */
export function trimRestrictedPathPrefixWithParam(path: string): string {
  const restrictedLinkMatch = /^\/:i:\/r\//;
  const parameterMatch = /\?.*$/;
  const prefixTrimmed = path.replace(restrictedLinkMatch, '/');

  // トリムされなかったときはここで返却
  if (path === prefixTrimmed) return path;

  // トリムが発生したときは更にパラメータをトリム
  return prefixTrimmed.replace(parameterMatch, '');
}

/**
 * pick the guid part from subEntityId if it is shared link format
 * if not just return blank string
 * @param subEntityId
 */
export function getGuidFromSubEntityId(subEntityId: string | undefined | null): string {
  if (typeof subEntityId !== 'string') return '';
  if (!isSharedLinkCommand(subEntityId)) {
    return '';
  }
  return subEntityId.substring(11);
}
