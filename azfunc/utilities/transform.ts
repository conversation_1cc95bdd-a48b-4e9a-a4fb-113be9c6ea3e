import { ISharePointListsResponse } from "../types/ISharePointListResponse";
import { ISharePointListsSingleResponse } from "../types/ISharePointListsSingleResponse";

/**
 * make a new response whose category1 is remapped with the other column
 * that specified with the environment variable
 * @param post a SharePoint single list item response
 */
export function remapPostCategorySingle(
  post: ISharePointListsSingleResponse,
): ISharePointListsSingleResponse {
    return post;
}

/**
 * make a new response whose category1 is remapped with the other column
 * that specified with the environment variable
 * @param res a SharePoint list response
 */
export function remapPostCategory(
  res: ISharePointListsResponse,
): ISharePointListsResponse {
  if (!Array.isArray(res.value)) {
    return res;
  }

  return {
    ...res,
    value: res.value.map((single) => remapPostCategorySingle(single)),
  };
}
