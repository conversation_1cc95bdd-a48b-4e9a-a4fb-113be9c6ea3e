import { createCamlBodyWithSearchWords } from './sharepoint';
import { TokenProvider } from '../types/TokenProvider';
import { Client } from '@microsoft/microsoft-graph-client';
import { createSpClient } from './spClient';
import { ISearchConditionTree } from '../types/ISearchParameters';

jest.mock('./spClient');

// authのmock
const tokenProviderMock = jest.fn().mockResolvedValue('token-mock');

const createSearchWords = (searchWords: string[] ) => ({
  userInputs: searchWords,
  synonyms: {},
  combinedWords: searchWords,
});

// @avanade-teams/authのmock
const getMock = jest.fn();
const postMock = jest.fn();
const selectMock = jest.fn();
const clientMock = {
  api: jest.fn().mockReturnThis(),
  expand: jest.fn().mockReturnThis(),
  orderby: jest.fn().mockReturnThis(),
  filter: jest.fn().mockReturnThis(),
  top: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  responseType: jest.fn().mockReturnThis(),
  get: getMock,
  post: postMock,
};
const createSpClientMock = (createSpClient as jest.MockedFunction<(
  tokenProvider: TokenProvider,
  baseUrl: string,
  customHost: string,
) => Client | undefined>);

beforeAll(() => {
  jest.useFakeTimers();
});

beforeEach(() => {
  createSpClientMock.mockClear().mockReturnValue(clientMock as unknown as Client);
  jest.setSystemTime(new Date('2021-01-01T12:00:00.000+09:00'));
  Object.values(clientMock).forEach((mock) => {
    mock.mockClear();
  });
  postMock.mockClear();
});

afterAll(() => {
  jest.useRealTimers();
});

type IParams = {
  rowLimit: number,
  fields: string[],
  searchWords: ISearchConditionTree,
  filterByPresentField: boolean,
};

describe('createCamlBodyWithSearchWords', () => {
  // 共通のXML仕様を検査
  function validateXmlSpec(xml: string, includePresentField: boolean): void {
    // 仕様 必要なフィールドが存在する
    expect(xml).toMatch('<FieldRef Name="GUID" />');
    expect(xml).toMatch('<FieldRef Name="Modified" />');
    // 仕様 releaseStateが"公開"
    expect(xml).toMatch('<Eq><FieldRef Name="releaseState" /><Value Type="Text">公開</Value></Eq>');
    // 仕様 presentPeriodがUTC現在日の0時
    // 仕様 Type="DateTime"にはStorageTZ="TRUE"を付与する
    if (includePresentField) {
      expect(xml).toMatch('<Or><IsNull><FieldRef Name="presentPeriod" /></IsNull><Geq><FieldRef Name="presentPeriod" /><Value Type="DateTime" StorageTZ="TRUE">2020-12-31T15:00:00.000Z</Value></Geq></Or>');
    } else {
      expect(xml).not.toMatch('<Or><IsNull><FieldRef Name="presentPeriod" /></IsNull><Geq><FieldRef Name="presentPeriod" /><Value Type="DateTime" StorageTZ="TRUE">2020-12-31T15:00:00.000Z</Value></Geq></Or>');
    }
    // 仕様 OrderByがModified降順
    expect(xml).toMatch('<OrderBy><FieldRef Name="Modified" Ascending="FALSE" /></OrderBy>');
    // 仕様 RowLimitが200 + 1
    expect(xml).toMatch('<RowLimit>201</RowLimit>');
  }

  const defaultParams: IParams = {
    rowLimit: 201,
    fields: ['field1', 'field2', 'field3'],
    searchWords: { operator: 'And', operand: ['"searchWord1\'', '"searchWord2\'', '"searchWord3\'']},
    filterByPresentField: true,
  };

  it('includes presend period clause if filterByPresentPeriod is set', () => {
    // arrange
    const params = defaultParams;
    // act
    const result = createCamlBodyWithSearchWords(params.rowLimit, params.fields, params.searchWords, params.filterByPresentField, undefined, undefined);
    // assert

    const xml = result.query.ViewXml;
    validateXmlSpec(xml, true);
  });

  it('includes presend period clause if filterByPresentPeriod is set', () => {
    // arrange
    const params: IParams = {
      ...defaultParams,
      filterByPresentField: false,
    }
    // act
    const result = createCamlBodyWithSearchWords(params.rowLimit, params.fields, params.searchWords, params.filterByPresentField, undefined, undefined);
    // assert

    const xml = result.query.ViewXml;
    validateXmlSpec(xml, false);
  });

  it('does not include any words query if searchWords is empty', () => {
    // arrange
    const params: IParams = {
      ...defaultParams,
      searchWords: {operator: 'And', operand: []},
    }
    // act
    const result = createCamlBodyWithSearchWords(params.rowLimit, params.fields, params.searchWords, params.filterByPresentField, undefined, undefined);
    // assert

    const xml = result.query.ViewXml;
    validateXmlSpec(xml, true);
    expect(xml).not.toMatch('<Contains><FieldRef Name="field1" />');
  });

  it('does not include any words query if fields is empty', () => {
    // arrange
    const params: IParams = {
      ...defaultParams,
      fields: [],
    }
    // act
    const result = createCamlBodyWithSearchWords(params.rowLimit, params.fields, params.searchWords, params.filterByPresentField, undefined, undefined);
    // assert

    const xml = result.query.ViewXml;
    validateXmlSpec(xml, true);
    expect(xml).not.toMatch('<Value Type="Text">&quot;searchWord1&apos;</Value>');
  });

  it('does include each words query for each field if both searchWords and fields are specified', () => {
    // arrange
    const params: IParams = defaultParams;
    // act
    const result = createCamlBodyWithSearchWords(params.rowLimit, params.fields, params.searchWords, params.filterByPresentField, undefined, undefined);
    // assert

    const xml = result.query.ViewXml;
    validateXmlSpec(xml, true);
    expect(xml).toMatch('<Contains><FieldRef Name="field1" /><Value Type="Text">&quot;searchWord1&apos;</Value></Contains>');
    expect(xml).toMatch('<Contains><FieldRef Name="field2" /><Value Type="Text">&quot;searchWord1&apos;</Value></Contains>');
    expect(xml).toMatch('<Contains><FieldRef Name="field3" /><Value Type="Text">&quot;searchWord1&apos;</Value></Contains>');
    expect(xml).toMatch('<Contains><FieldRef Name="field1" /><Value Type="Text">&quot;searchWord2&apos;</Value></Contains>');
    expect(xml).toMatch('<Contains><FieldRef Name="field2" /><Value Type="Text">&quot;searchWord2&apos;</Value></Contains>');
    expect(xml).toMatch('<Contains><FieldRef Name="field3" /><Value Type="Text">&quot;searchWord2&apos;</Value></Contains>');
    expect(xml).toMatch('<Contains><FieldRef Name="field1" /><Value Type="Text">&quot;searchWord3&apos;</Value></Contains>');
    expect(xml).toMatch('<Contains><FieldRef Name="field2" /><Value Type="Text">&quot;searchWord3&apos;</Value></Contains>');
    expect(xml).toMatch('<Contains><FieldRef Name="field3" /><Value Type="Text">&quot;searchWord3&apos;</Value></Contains>');
  });

  it('does include previous page', () => {
    // arrange
    const params: IParams = defaultParams;
    // act
    const result = createCamlBodyWithSearchWords(params.rowLimit, params.fields, params.searchWords, params.filterByPresentField, 'previd', 'prevmodified');
    // assert

    expect(result.query.ListItemCollectionPosition).not.toBeUndefined();
    expect(result.query.ListItemCollectionPosition!.PagingInfo).toBe('Paged=TRUE&p_Modified=prevmodified&p_ID=previd');
  });
  it('parses the expression', () => {
    // arrange
    const params: IParams = {
      ...defaultParams,
      searchWords: {operator: 'And', operand: [{operator: 'Or', operand: ['abc', 'def']}]},
    };

    // act
    const result = createCamlBodyWithSearchWords(params.rowLimit, params.fields, params.searchWords, params.filterByPresentField, undefined, undefined);

    // assert
    const xml = result.query.ViewXml;
    expect(xml).toContain("<Or><Or><Contains><FieldRef Name=\"field1\" /><Value Type=\"Text\">abc</Value></Contains><Or><Contains><FieldRef Name=\"field2\" /><Value Type=\"Text\">abc</Value></Contains><Contains><FieldRef Name=\"field3\" /><Value Type=\"Text\">abc</Value></Contains></Or></Or><Or><Contains><FieldRef Name=\"field1\" /><Value Type=\"Text\">def</Value></Contains><Or><Contains><FieldRef Name=\"field2\" /><Value Type=\"Text\">def</Value></Contains><Contains><FieldRef Name=\"field3\" /><Value Type=\"Text\">def</Value></Contains></Or></Or></Or>");
  });

  it('excludes quotes around word', () => {
    // arrange
    const params: IParams = {
      ...defaultParams,
      searchWords: {operator: 'And', operand: ['"abc"', '"OR"', '"def"']},
    };

    // act
    const result = createCamlBodyWithSearchWords(params.rowLimit, params.fields, params.searchWords, params.filterByPresentField, undefined, undefined);

    // assert
    const xml = result.query.ViewXml;
    expect(xml).toMatch('<Value Type="Text">abc</Value>');
    expect(xml).toMatch('<Value Type="Text">OR</Value>');
    expect(xml).toMatch('<Value Type="Text">def</Value>');
  });
});
