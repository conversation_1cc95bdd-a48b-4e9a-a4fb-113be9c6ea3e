export type ValueOf<T> = T[keyof T];

// teamsのユーザー状態の種類
export const UserActivity = {
  AVAILABLE: 'Available',
  AVAILABLE_IDLE: 'AvailableIdle',
  AWAY: 'Away',
  BE_RIGHT_BACK: 'BeRightBack',
  BUSY: 'Busy',
  BUSY_IDLE: 'BusyIdle',
  DO_NOT_DISTURB: 'DoNotDisturb',
  OFFLINE: 'Offline',

  // 未確定状態用
  BLANK: '',
};

// teamsのユーザー状態の種類をtypeに変換したもの
export type PresenceType = ValueOf<typeof UserActivity>;

/**
 * check the unknown type parameter and always return as string
 * @param param
 */
export function handleUnknownString(param: unknown): string {
  if (typeof param !== 'string') return '';
  return param;
}

/**
 * check the unknown type parameter and always return as { category: string } type
 * @param param
 */
export function handleUnknownSpoColumns(
  param: unknown | { category?: unknown },
): { category: string } {
  if (!param || typeof param !== 'object') return { category: '' };
  const objParam = param as { category?: unknown };
  return { category: handleUnknownString(objParam.category) };
}
