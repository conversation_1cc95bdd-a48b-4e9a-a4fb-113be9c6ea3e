import dayjs = require("dayjs");
import CamlBuilder = require("camljs");
import 'isomorphic-fetch';
import { IExpression } from 'camljs';
import { ISearchWords } from "../types/ISearchWords";
import { ISharePointListItemResponseWithAttachment } from "../types/ISharePointListItemResponseWithAttachment";
import { ISharePointListsResponse } from "../types/ISharePointListResponse";
import { getZeroToday } from "../utilities/date";
import { ISearchConditionTree } from "../types/ISearchParameters";

export type FetchSPODetail = (
  baseUrl: string, editLink: string,
) => Promise<ISharePointListItemResponseWithAttachment>;

export type FetchSPOList = (
  baseUrl: string, listGUID: string, substringofParams?: string[],
) => Promise<ISharePointListsResponse>;

export type FetchSPOListByCaml = (
  baseUrl: string, listGUID: string, searchWords?: ISearchWords,
) => Promise<ISharePointListsResponse>;

export type FetchUpdatesByIds = (
  baseUrl: string, listGUID: string, ids: string[],
) => Promise<ISharePointListsResponse>

export type UseSharePointApiReturnType = [
  fetchDetail?: FetchSPODetail,
  fetchList?: FetchSPOList,
  // fetchFileBlob?: FetchFileBlob,
  fetchListByCaml?: FetchSPOListByCaml,
  fetchUpdatesByIds?: FetchUpdatesByIds,
];

export const UseSharePointApiError = {
  TOKEN_PROVIDER_NOT_AVAILABLE: 'tokenProvider is not provided yet',
  REQUIRED_PARAM_NOT_AVAILABLE: 'required parameters are not provided',
};

/**
 * SharePointのサイトディレクトリ
 */
const SPO_SITES_DIR = '/sites/';

/**
 * CAMLでリスト一覧を取得するためのURLを生成する
 * @param listGUID
 */
export function createListUrlOfCaml(listGUID: string): string {
  return `web/Lists('${listGUID}')/GetItems`;
}

/**
 * キーワード検索条件を作成する
 *
 * @param {string} word
 * @param {fields} fields for searching
 * @return {*}  {IExpression[]}
 */
function createWordSearchCondition(
  word: string,
  fields: string[],
): IExpression {
  const bareWord = /^"(.*)"$/.exec(word)?.[1] ?? word;
  const expressions = fields.map((f) => CamlBuilder.Expression().TextField(f).Contains(bareWord));
  return CamlBuilder.Expression().Any(expressions);
}

interface ICamlBody {
  query: {
    ViewXml: string;
    ListItemCollectionPosition?: {
      PagingInfo: string,
    },
  }
}

function getReleaseStateCamlQuery() {
  // 公開記事クエリ releaseStateが公開である
  return CamlBuilder.Expression().TextField('releaseState').EqualTo('公開');
}

function getPresentPeriodCamlQuery() {
  // 掲示期限クエリ 現在日を含む未来 または 掲示期限の設定なし(null)
  return CamlBuilder.Expression().Any(
    CamlBuilder.Expression().TextField('presentPeriod').IsNull(),
    CamlBuilder.Expression().DateField('presentPeriod').GreaterThanOrEqualTo(getZeroToday()),
  );
}

/**
 * CAMLクエリを生成する
 * @param rowLimit 取得する件数の上限値
 * @param searchWords 検索キーワードのデータ型(@interface ISearchWords)
 */
export function createCamlBodyWithSearchWords(rowLimit: number, fields: string[], searchWords: ISearchConditionTree, filterByPresentPeriod: boolean, prevId: string | undefined, prevModified: string | undefined): ICamlBody {
  // 公開記事クエリ releaseStateが公開である
  const releaseStateQuery = getReleaseStateCamlQuery();
  // 掲示期限クエリ 現在日を含む未来 または 掲示期限の設定なし(null)
  const presentPeriodQuery = filterByPresentPeriod ? getPresentPeriodCamlQuery() : undefined;

  // 題名、分類、記事本文を対象とするOR条件グループ全件のAND条件を作成
  const wordsQuery = buildWordsQuery(searchWords, fields);

  const queries = [
    ...(wordsQuery ? [wordsQuery] : []),
    releaseStateQuery,
    ...(presentPeriodQuery ? [presentPeriodQuery] : [])
  ];

  const camlBody = new CamlBuilder()
    .View(['GUID', 'Modified'])
    .RowLimit(rowLimit)
    .Query()
    .Where()
    .All(...queries)
    .OrderByDesc('Modified')
    .ToString()
    // attach StorageTZ to compare in UTC
    // https://sharepoint.stackexchange.com/questions/244261/caml-query-compare-datetime-in-utc
    .replace(/<Value Type="DateTime">/g, '<Value Type="DateTime" StorageTZ="TRUE">');

  if (prevId) {
    return {
      query: {
        ViewXml: camlBody,
        ListItemCollectionPosition: {
          PagingInfo: `Paged=TRUE&p_Modified=${prevModified}&p_ID=${prevId}`
        },
      }
    }
  } else {
    return { query: { ViewXml: camlBody } };
  }
}

function buildWordsQuery(searchWords: ISearchConditionTree, fields: string[]): CamlBuilder.IExpression {
  if (typeof searchWords === "string") {
    return createWordSearchCondition(searchWords, fields);
  }

  switch (searchWords.operator) {
    case "And":
      return CamlBuilder.Expression().All(searchWords.operand.map(i => buildWordsQuery(i, fields)));
    case "Or":
      return CamlBuilder.Expression().Any(searchWords.operand.map(i => buildWordsQuery(i, fields)));
    default:
      throw new Error("unknown operator");
  }
}
