import { blankToDash, trimPreSufSlash } from './text';

describe('utilities/text', () => {
  describe('blankToDash', () => {
    it('should return dash if parameter is null', () => {
      expect(
        blankToDash(null),
      ).toBe('–');
    });
    it('should return dash if parameter is undefined', () => {
      expect(
        blankToDash(undefined),
      ).toBe('–');
    });
    it('should return dash if parameter is blank', () => {
      expect(
        blankToDash(''),
      ).toBe('–');
    });
    it('should return string as it is if parameter is not blank', () => {
      expect(
        blankToDash('test'),
      ).toBe('test');
    });
  });

  describe('trimPreSufSlash', () => {
    it('should return blank if parameter is null', () => {
      expect(
        trimPreSufSlash(null),
      ).toBe('');
    });
    it('should return blank if parameter is undefined', () => {
      expect(
        trimPreSufSlash(undefined),
      ).toBe('');
    });
    it('should return blank if parameter is blank', () => {
      expect(
        trimPreSufSlash(''),
      ).toBe('');
    });
    it('should return the input as is if if it does not have slashes at the head or end', () => {
      expect(
        trimPreSufSlash('no/head/or/end/slash'),
      ).toBe('no/head/or/end/slash');
    });
    it('should trim the slash at the head and end', () => {
      expect(
        trimPreSufSlash('//slash/at/the/head/should/be/removed'),
      ).toBe('slash/at/the/head/should/be/removed');
      expect(
        trimPreSufSlash('slash/at/the/end/should/be/removed//'),
      ).toBe('slash/at/the/end/should/be/removed');
      expect(
        trimPreSufSlash('/both/head/and/end/slash/should/be/removed/'),
      ).toBe('both/head/and/end/slash/should/be/removed');
    });
  });
});
