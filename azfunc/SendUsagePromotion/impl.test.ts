import { Context, Logger } from '@azure/functions';
import { Client } from '@microsoft/microsoft-graph-client';
import { User } from '@microsoft/microsoft-graph-types';
// import { ClientSecretCredentialMock } from '../mocks/azure';
import { fetchGroupUsers, fetchJsonBatch, createInstalledAppsUrlWithAppIdFilter } from '../utilities/graph';
import { IBatchResponses } from '../utilities/models';
import * as impl from './impl';

jest.mock('../utilities/graph');

describe('SendUsagePromotion', () => {

  const clientMock = jest.fn();
  const loggerMock = jest.fn();
  const logger = loggerMock as unknown as Context['log'];
  logger.warn = loggerMock;
  const fetchGroupUsersMock = fetchGroupUsers as jest.Mock;
  const fetchJsonBatchMock = fetchJsonBatch as jest.Mock;
  const createInstalledAppsUrlWithAppIdFilterMock = createInstalledAppsUrlWithAppIdFilter as jest.Mock;

  createInstalledAppsUrlWithAppIdFilterMock.mockImplementation((uId: string, tId: string) => {
    return `test/${tId}/${uId}`
  });

  beforeEach(() => {
    clientMock.mockClear();
    loggerMock.mockClear();
    fetchGroupUsersMock.mockClear();
    fetchJsonBatchMock.mockClear();
    createInstalledAppsUrlWithAppIdFilterMock.mockClear();
  });

  describe('createCredential', () => {
    describe('when one of the parameters is missing', () => {
      it('should return undefined', () => {
        expect(
          impl.createCredential('', 'b', 'c')
        ).toBeUndefined();
        expect(
          impl.createCredential('a', '', 'c')
        ).toBeUndefined();

        expect(
          impl.createCredential('a', 'b', '')
        ).toBeUndefined();
      });
    });

  //　リファクタリング対象:以下テストはmiTeneと同じコードにもかかわらずエラーで落ちる。
  // またテストの正当性に疑問が残るためコメントアウトするが、追々内容精査し復活OR削除する。
  //   describe('when all of the parameters are filled', () => {
  //     // const mockTokenCredential = { mock: 'mock-token-credential'};
  //     // const ClientSecretCredential = jest.spyOn(impl, 'createCredential')
  //     // .mockReturnValueOnce({getToken: 
  //     //   jest.fn().mockImplementation(() => {
  //     //     return {
  //     //       mock: 'mock-token-credential',
  //     //     };
  //     //   })});
    
  //     // jest.spyOn(impl, 'sendActivitiesToUsers')
  //     // .mockResolvedValueOnce('mock-token-credential');
  //     //   {
  //     //     mock: 'mock-token-credential',
  //     //   };
  //     // });
  //     it('should return tokenProvider instance', () => {
  //     //  const ClientSecretCredentialMock = ClientSecretCredential;
  //       expect(
  //         impl.createCredential('a', 'b', 'c')
  //       ).toStrictEqual({
  //         // mockConstructor: {}が返ってきている
  //         // Mockが呼ばれてない
  //         mock: 'mock-token-credential'
  //       });
  //       console.log(ClientSecretCredentialMock)
  //       expect(ClientSecretCredentialMock).toBeCalledTimes(1);
  //       expect(ClientSecretCredentialMock).toBeCalledWith('a', 'b', 'c');
  //     });
  //   });
  });

  describe('fetchTargetUsersByGroupIds', () => {
    describe('when fetchGroupUsers returns 0 length', () => {
      it('should return blank array', async () => {
        fetchGroupUsersMock.mockResolvedValueOnce([]);
        await expect(
          impl.fetchTargetUsersByGroupIds(logger, clientMock as unknown as Client, ['abc'], '123')
        ).resolves.toStrictEqual([]);
        expect(loggerMock).toBeCalledTimes(1);
        expect(loggerMock).toBeCalledWith('AllGroupUsers(1/1): 0, []');
      });
    });

    describe('when fetchGroupUsers returns some users', () => {
      const users = [
        { id: '1' },
        { id: '2' },
        { id: '3' },
        { id: '4' },
        { id: '5' },
        { id: '6' },
        { id: '7' },
        { id: '8' },
        { id: '9' },
        { id: '10' },
        { id: '11' },
        { id: '12' },
        { id: '13' },
        { id: '14' },
        { id: '15' },
        { id: '16' },
        { id: '17' },
        { id: '18' },
        { id: '19' },
        { id: '20' },
        // 21件あるのでbatchAPIは2回呼ばれる
        { id: '21' },
      ];
      fetchGroupUsersMock.mockResolvedValue(users);

      describe('when fetchJsonBatch returns  result', () => {
        beforeEach(() => {
          fetchJsonBatchMock.mockResolvedValueOnce({
            responses: [
              {
                id: 'abc',
                status: 200,
                body: {
                  value: [{
                    id: '123',
                  }],
                },
              },
              {
                id: 'efg',
                status: 404,
                body: {
                  value: [{
                    id: '456',
                  }],
                },
              },
              {
                id: 'hij',
                status: 200,
                body: {
                  value: [{
                    id: '789',
                  }],
                },
              },
            ],
          } as IBatchResponses);

          fetchJsonBatchMock.mockResolvedValueOnce({
            responses: [
              {
                id: 'klm',
                status: 200,
                body: {
                  value: [{
                    id: '123',
                  }],
                },
              },
            ],
          } as IBatchResponses);
        });

        afterEach(() => {
          fetchJsonBatchMock.mockRestore();
        });

        it('should return flatten responses', async () => {
          const users = [
            { id: '1' },
            { id: '2' },
            { id: '3' },
            { id: '4' },
            { id: '5' },
            { id: '6' },
            { id: '7' },
            { id: '8' },
            { id: '9' },
            { id: '10' },
            { id: '11' },
            { id: '12' },
            { id: '13' },
            { id: '14' },
            { id: '15' },
            { id: '16' },
            { id: '17' },
            { id: '18' },
            { id: '19' },
            { id: '20' },
            // 21件あるのでbatchAPIは2回呼ばれる
            { id: '21' },
          ];
          fetchGroupUsersMock.mockResolvedValue(users);
          expect(
            await impl.fetchTargetUsersByGroupIds(
              logger,
              clientMock as unknown as Client,
              [
                'abc',
              ],
              '123')
          ).toStrictEqual([
            {
              id: 'abc',
              status: 200,
              body: {
                value: [{
                  id: '123',
                }],
              },
            },
            {
              id: 'hij',
              status: 200,
              body: {
                value: [{
                  id: '789',
                }],
              },
            },
            // batchが2回呼ばれるので結果は2回分結合される
            {
              id: 'klm',
              status: 200,
              body: {
                value: [{
                  id: '123',
                }],
              },
            },
          ]);
          expect(fetchGroupUsersMock).toBeCalledWith(clientMock, 'abc');
          expect(loggerMock).toBeCalledTimes(2);
          expect(logger).not.toHaveBeenCalledWith({ status: 200 });
          expect(loggerMock).toBeCalledWith(`AllGroupUsers(1/1): 21, ["1","2","3","4","5","6","7","8","9","10","11","12","13","14","15","16","17","18","19","20","21"]`);
        });
      });
    });
  });

  describe('sendActivitiesToUsers', () => {
    it('should return flatten response', async () => {
      fetchJsonBatchMock.mockResolvedValue({
        responses: [
          {
            id: '123',
            status: 203,
            body: {
              value: [{
                id: '123',
              }],
            },
          },
        ],
      } as IBatchResponses);

      expect(
        await impl.sendActivitiesToUsers(
          loggerMock as unknown as Logger,
          clientMock as unknown as Client,
          [
            { id: '123' },
          ] as User[],
          'お知らせが届きました。',
        )
      ).toStrictEqual([
        {
          id: '123',
          status: 203,
          body: {
            value: [{
              id: '123',
            }],
          },
        },
      ]);

      expect(fetchJsonBatchMock).toBeCalledWith(
        loggerMock,
        clientMock,
        [
          {
            id: '123',
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            url: '/users/123/teamwork/sendActivityNotification',
            body: {
              activityType: 'company',
              chainId: 1,
              templateParameters: [
                {
                  name: 'name',
                  value: 'お知らせが届きました。',
                },
              ],
              topic: {
                source: 'entityUrl',
                value: '',
              },
            },
          },
        ],
      );

    });
  });

});
