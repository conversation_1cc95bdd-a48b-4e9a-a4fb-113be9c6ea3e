<#
	.SYNOPSIS
	リソースグループへのリソースのデプロイを行います

	.DESCRIPTION
	デプロイに以下のファイルを使用します。
	- template.json: メインのテンプレートファイル
	- parameters.${Env}.json: template.jsonに対応するパラメータファイル
	- deploy_settings/${Env}_variables.ps1: 環境設定スクリプト
	- Register-ResourceProviders.ps1: リソース登録スクリプト

	.PARAMETER Env
	対象の環境を指定します。
	指定した環境に対する設定ファイルがdeploy_settingフォルダ内に、
	指定した環境に対するパラメータファイルが同一フォルダ内に必要です。

	.OUTPUTS
	ARM テンプレート適用時の出力が出力されます
#>

param (
	# デプロイしたい環境を指定する。ENVの指定は必須。
	[Parameter(Mandatory=$true)][string]$Env,
	[Parameter(Mandatory=$false)][string]$Company
)

# 宣言を必須にする
Set-StrictMode -Version Latest

# エラー時にスクリプトを終了させる
$ErrorActionPreference = "Stop"

. "${PSScriptRoot}\deploy_settings\${Env}_variables.ps1"

$clientSecret = ConvertTo-SecureString -String $ClientSecret -AsPlainText -Force
$clientCredential = New-Object System.Management.Automation.PSCredential($ClientId, $clientSecret)

Connect-AzAccount -ServicePrincipal -Credential $clientCredential -Tenant $TenantId

$providers = (
	'Microsoft.Web',
	'Microsoft.Storage',
	'Microsoft.insights',
	'Microsoft.operationalinsights',
	'Microsoft.Network'
)

<#
	.SYNOPSIS
	ARM テンプレートをデプロイします

	.PARAMETER Message
	処理開始時に出力するメッセージを指定します

	.PARAMETER Suffix
	パラメータ名およびテンプレート名のsuffixを指定します

	.OUTPUTS
	ARM テンプレート適用時の出力が出力されます
#>
function DeployResources {
	param (
		[string]$Message,
		[string]$Suffix,
		[string]$ParameterFileName,
		[string]$TemplateFileName,
		[string]$ResourceGroupName
	)

	$parameterFilePath = "${PSScriptRoot}\${ParameterFileName}"
	$templateFilePath = "${PSScriptRoot}\${TemplateFileName}"

	Write-Output $Message

	# ARMテンプレートの適用
	Write-Host "New-AzResourceGroupDeployment -ResourceGroupName $ResourceGroupName -TemplateFile $templateFilePath -TemplateParameterFile $parameterFilePath"
	$result = New-AzResourceGroupDeployment -ResourceGroupName $ResourceGroupName -TemplateFile $templateFilePath -TemplateParameterFile $parameterFilePath

	# 一旦出力
	$result
}

. ${PSScriptRoot}\Register-ResourceProviders.ps1 -providers $providers

DeployResources -Message "Start deploy!" `
	-ParameterFileName "params_${Env}\parameters_rg_sources.${Env}.json" `
	-TemplateFileName "template_rg-sources.json" `
	-ResourceGroupName $ResourceGroupNames['sources']