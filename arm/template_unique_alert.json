{
  "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
  "contentVersion": "1.0.0.0",
  "parameters": {
    "global-location": {
      "type": "string"
    },
    "alert-enabled": {
      "type": "bool"
    },
    "alert-actionGroupResourceGroupName": {
      "type": "string"
    },
    "alert-actionGroupsName": {
      "type": "string"
    },
    "resourceGroup-Name": {
      "type": "string"
    },
    "appService-siteName": {
      "type": "string"
    },
    "appService-DataOutAlertsName": {
      "type": "string"
    },
    "appService-http4xxAlertsName": {
      "type": "string"
    },
    "appService-http5xxAlertsName": {
      "type": "string"
    },
    "appService-responseAlertsName": {
      "type": "string"
    },
    "appService-ResponseTimeAlertsName": {
      "type": "string"
    },
    "functions-siteName": {
      "type": "string"
    },
    "functions-AverageResponseTimeAlert": {
      "type": "string"
    },
    "functions-ResponseTimeAlert": {
      "type": "string"
    },
    "resourceHealth-ActivityLogAlerts": {
      "type": "string"
    },
    "resourceHealth-Alerts": {
      "type": "string"
    },
    "storage-accountName":{
      "type": "string"
    },
    "storage-OverEgressAlertsName": {
      "type": "string"
    },
    "storage-OverEgressAlertsName-threshold": {
      "type": "int"
    },
    "storage-OverTransactionsAlertsName": {
      "type": "string"
    },
    "storage-OverTransactionsAlertsName-threshold": {
      "type": "int"
    },
    "storage-SuccessServerLatencyAlertsName": {
      "type": "string"
    },
    "storage-SuccessServerLatencyAlertsName-threshold": {
      "type": "int"
    },
    "storage-UsedCapacityAlertsName": {
      "type": "string"
    }
  },
  "resources": [
    {
      "condition": "[parameters('alert-enabled')]",
      "type": "microsoft.insights/metricAlerts",
      "apiVersion": "2018-03-01",
      "name": "[parameters('appService-DataOutAlertsName')]",
      "location": "global",
      "properties": {
        "description": "App Serviceの送信データ量（Data Out）",
        "severity": 3,
        "enabled": true,
        "scopes": [
          "[resourceId('Microsoft.Web/sites', parameters('appService-siteName'))]"
        ],
        "evaluationFrequency": "PT5M",
        "windowSize": "PT5M",
        "criteria": {
          "allOf": [
            {
              "threshold": 10000000,
              "name": "Metric1",
              "metricNamespace": "Microsoft.Web/sites",
              "metricName": "BytesSent",
              "operator": "GreaterThan",
              "timeAggregation": "Average",
              "criterionType": "StaticThresholdCriterion"
            }
          ],
          "odata.type": "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
        },
        "autoMitigate": false,
        "targetResourceType": "Microsoft.Web/sites",
        "targetResourceRegion": "[parameters('global-location')]",
        "actions": [
          {
            "actionGroupId": "[resourceId(parameters('alert-actionGroupResourceGroupName'),'Microsoft.Insights/actionGroups', parameters('alert-actionGroupsName'))]",
            "webHookProperties": {}
          }
        ]
      }
    },
    {
      "condition": "[parameters('alert-enabled')]",
      "type": "microsoft.insights/metricAlerts",
      "apiVersion": "2018-03-01",
      "name": "[parameters('appService-http4xxAlertsName')]",
      "location": "global",
      "properties": {
        "description": "HTTPステータスコード4xxのリクエスト",
        "severity": 2,
        "enabled": true,
        "scopes": [
          "[resourceId('Microsoft.Web/sites', parameters('appService-siteName'))]"
        ],
        "evaluationFrequency": "PT5M",
        "windowSize": "PT15M",
        "criteria": {
          "allOf": [
            {
              "threshold": 30,
              "name": "Metric1",
              "metricNamespace": "Microsoft.Web/sites",
              "metricName": "Http4xx",
              "dimensions": [
                {
                  "name": "Instance",
                  "operator": "Include",
                  "values": [
                    "*"
                  ]
                }
              ],
              "operator": "GreaterThan",
              "timeAggregation": "Total",
              "skipMetricValidation": false,
              "criterionType": "StaticThresholdCriterion"
            }
          ],
          "odata.type": "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
        },
        "autoMitigate": false,
        "targetResourceType": "Microsoft.Web/sites",
        "targetResourceRegion": "[parameters('global-location')]",
        "actions": [
          {
            "actionGroupId": "[resourceId(parameters('alert-actionGroupResourceGroupName'),'Microsoft.Insights/actionGroups', parameters('alert-actionGroupsName'))]",
            "webHookProperties": {}
          }
        ]
      }
    },
    {
      "condition": "[parameters('alert-enabled')]",
      "type": "microsoft.insights/metricAlerts",
      "apiVersion": "2018-03-01",
      "name": "[parameters('appService-http5xxAlertsName')]",
      "location": "global",
      "properties": {
        "description": "HTTPステータスコード5xxのリクエスト",
        "severity": 2,
        "enabled": true,
        "scopes": [
          "[resourceId('Microsoft.Web/sites', parameters('appService-siteName'))]"
        ],
        "evaluationFrequency": "PT5M",
        "windowSize": "PT15M",
        "criteria": {
          "allOf": [
            {
              "threshold": 30,
              "name": "Metric1",
              "metricNamespace": "Microsoft.Web/sites",
              "metricName": "Http5xx",
              "operator": "GreaterThan",
              "timeAggregation": "Total",
              "skipMetricValidation": false,
              "criterionType": "StaticThresholdCriterion"
            }
          ],
          "odata.type": "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
        },
        "autoMitigate": false,
        "targetResourceType": "Microsoft.Web/sites",
        "targetResourceRegion": "[parameters('global-location')]",
        "actions": [
          {
            "actionGroupId": "[resourceId(parameters('alert-actionGroupResourceGroupName'),'Microsoft.Insights/actionGroups', parameters('alert-actionGroupsName'))]",
            "webHookProperties": {}
          }
        ]
      }
    },
    {
      "condition": "[parameters('alert-enabled')]",
      "type": "microsoft.insights/metricAlerts",
      "apiVersion": "2018-03-01",
      "name": "[parameters('appService-responseAlertsName')]",
      "location": "global",
      "properties": {
        "description": "App Service側のリクエスト処理の平均時間超過検知アラート",
        "severity": 2,
        "enabled": true,
        "scopes": [
          "[resourceId('Microsoft.Web/sites', parameters('appService-siteName'))]"
        ],
        "evaluationFrequency": "PT5M",
        "windowSize": "PT5M",
        "criteria": {
          "allOf": [
            {
              "threshold": 0.5,
              "name": "Metric1",
              "metricNamespace": "Microsoft.Web/sites",
              "metricName": "AverageResponseTime",
              "operator": "GreaterThan",
              "timeAggregation": "Average",
              "criterionType": "StaticThresholdCriterion"
            }
          ],
          "odata.type": "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
        },
        "autoMitigate": false,
        "targetResourceType": "Microsoft.Web/sites",
        "targetResourceRegion": "[parameters('global-location')]",
        "actions": [
          {
            "actionGroupId": "[resourceId(parameters('alert-actionGroupResourceGroupName'),'Microsoft.Insights/actionGroups', parameters('alert-actionGroupsName'))]",
          }
        ]
      }
    },
    {
      "condition": "[parameters('alert-enabled')]",
      "type": "microsoft.insights/metricAlerts",
      "apiVersion": "2018-03-01",
      "name": "[parameters('appService-ResponseTimeAlertsName')]",
      "location": "global",
      "properties": {
        "description": "応答時間の平均超過アラート",
        "severity": 2,
        "enabled": true,
        "scopes": [
          "[resourceId('Microsoft.Web/sites', parameters('appService-siteName'))]"
        ],
        "evaluationFrequency": "PT5M",
        "windowSize": "PT5M",
        "criteria": {
          "allOf": [
            {
              "threshold": 0.5,
              "name": "Metric1",
              "metricNamespace": "Microsoft.Web/sites",
              "metricName": "HttpResponseTime",
              "operator": "GreaterThan",
              "timeAggregation": "Average",
              "criterionType": "StaticThresholdCriterion"
            }
          ],
          "odata.type": "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
        },
        "autoMitigate": false,
        "targetResourceType": "Microsoft.Web/sites",
        "targetResourceRegion": "[parameters('global-location')]",
        "actions": [
          {
            "actionGroupId": "[resourceId(parameters('alert-actionGroupResourceGroupName'),'Microsoft.Insights/actionGroups', parameters('alert-actionGroupsName'))]",
          }
        ]
      }
    },
    {
      "condition": "[parameters('alert-enabled')]",
      "type": "microsoft.insights/metricalerts",
      "apiVersion": "2018-03-01",
      "name": "[parameters('functions-AverageResponseTimeAlert')]",
      "location": "global",
      "properties": {
        "description": "Azure Functions側のリクエスト処理の平均時間超過検知アラート",
        "severity": 2,
        "enabled": true,
        "scopes": [
          "[resourceId('Microsoft.Web/sites', parameters('functions-siteName'))]"
        ],
        "evaluationFrequency": "PT5M",
        "windowSize": "PT5M",
        "criteria": {
          "allOf": [
            {
              "threshold": 0.75,
              "name": "Metric1",
              "metricNamespace": "Microsoft.Web/sites",
              "metricName": "AverageResponseTime",
              "operator": "GreaterThan",
              "timeAggregation": "Average",
              "criterionType": "StaticThresholdCriterion"
            }
          ],
          "odata.type": "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
        },
        "autoMitigate": false,
        "targetResourceType": "Microsoft.Web/sites",
        "targetResourceRegion": "[parameters('global-location')]",
        "actions": [
          {
            "actionGroupId": "[resourceId(parameters('alert-actionGroupResourceGroupName'),'Microsoft.Insights/actionGroups', parameters('alert-actionGroupsName'))]",
          }
        ]
      }
    },
    {
      "condition": "[parameters('alert-enabled')]",
      "type": "microsoft.insights/metricAlerts",
      "apiVersion": "2018-03-01",
      "name": "[parameters('functions-ResponseTimeAlert')]",
      "location": "global",
      "properties": {
        "description": "応答時間の平均超過アラート",
        "severity": 2,
        "enabled": true,
        "scopes": [
          "[resourceId('Microsoft.Web/sites', parameters('functions-siteName'))]"
        ],
        "evaluationFrequency": "PT5M",
        "windowSize": "PT5M",
        "criteria": {
          "allOf": [
            {
              "threshold": 0.75,
              "name": "Metric1",
              "metricNamespace": "Microsoft.Web/sites",
              "metricName": "HttpResponseTime",
              "operator": "GreaterThan",
              "timeAggregation": "Average",
              "criterionType": "StaticThresholdCriterion"
            }
          ],
          "odata.type": "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
        },
        "autoMitigate": false,
        "targetResourceType": "Microsoft.Web/sites",
        "targetResourceRegion": "[parameters('global-location')]",
        "actions": [
          {
            "actionGroupId": "[resourceId(parameters('alert-actionGroupResourceGroupName'),'Microsoft.Insights/actionGroups', parameters('alert-actionGroupsName'))]",
          }
        ]
      }
    },
    {
      "condition": "[parameters('alert-enabled')]",
      "type": "microsoft.insights/metricalerts",
      "apiVersion": "2018-03-01",
      "name": "[parameters('storage-OverEgressAlertsName')]",
      "location": "global",
      "properties": {
        "description": "ストレージアカウントのデータ通信量の急増を検知",
        "severity": 2,
        "enabled": true,
        "scopes": [
          "[resourceId('Microsoft.Storage/storageAccounts', parameters('storage-accountName'))]"
        ],
        "evaluationFrequency": "PT1H",
        "windowSize": "PT1H",
        "criteria": {
          "allOf": [
            {
              "threshold": "[parameters('storage-OverEgressAlertsName-threshold')]",
              "name": "Metric1",
              "metricNamespace": "Microsoft.Storage/storageAccounts",
              "metricName": "Egress",
              "operator": "GreaterThan",
              "timeAggregation": "Total",
              "criterionType": "StaticThresholdCriterion"
            }
          ],
          "odata.type": "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
        },
        "autoMitigate": false,
        "targetResourceType": "Microsoft.Storage/storageAccounts",
        "targetResourceRegion": "[parameters('global-location')]",
        "actions": [
          {
            "actionGroupId": "[resourceId(parameters('alert-actionGroupResourceGroupName'),'Microsoft.Insights/actionGroups', parameters('alert-actionGroupsName'))]",
          }
        ]
      }
    },
    {
      "condition": "[parameters('alert-enabled')]",
      "type": "microsoft.insights/metricalerts",
      "apiVersion": "2018-03-01",
      "name": "[parameters('storage-OverTransactionsAlertsName')]",
      "location": "global",
      "properties": {
        "description": "ストレージアカウントの処理回数急増を検知",
        "severity": 2,
        "enabled": true,
        "scopes": [
          "[resourceId('Microsoft.Storage/storageAccounts', parameters('storage-accountName'))]"
        ],
        "evaluationFrequency": "PT1H",
        "windowSize": "PT1H",
        "criteria": {
          "allOf": [
            {
              "threshold": "[parameters('storage-OverTransactionsAlertsName-threshold')]",
              "name": "Metric1",
              "metricNamespace": "Microsoft.Storage/storageAccounts",
              "metricName": "Transactions",
              "operator": "GreaterThan",
              "timeAggregation": "Total",
              "criterionType": "StaticThresholdCriterion"
            }
          ],
          "odata.type": "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
        },
        "autoMitigate": false,
        "targetResourceType": "Microsoft.Storage/storageAccounts",
        "targetResourceRegion": "[parameters('global-location')]",
        "actions": [
          {
            "actionGroupId": "[resourceId(parameters('alert-actionGroupResourceGroupName'),'Microsoft.Insights/actionGroups', parameters('alert-actionGroupsName'))]",
          }
        ]
      }
    },
    {
      "condition": "[parameters('alert-enabled')]",
      "type": "microsoft.insights/metricalerts",
      "apiVersion": "2018-03-01",
      "name": "[parameters('storage-UsedCapacityAlertsName')]",
      "location": "global",
      "properties": {
        "description": "ストレージの量超過検知アラート(2.5PB)※予想上限目安",
        "severity": 2,
        "enabled": true,
        "scopes": [
          "[resourceId('Microsoft.Storage/storageAccounts', parameters('storage-accountName'))]"
        ],
        "evaluationFrequency": "PT1H",
        "windowSize": "PT1H",
        "criteria": {
          "allOf": [
            {
              "threshold": ****************,
              "name": "Metric1",
              "metricNamespace": "Microsoft.Storage/storageAccounts",
              "metricName": "UsedCapacity",
              "operator": "GreaterThan",
              "timeAggregation": "Average",
              "skipMetricValidation": false,
              "criterionType": "StaticThresholdCriterion"
            }
          ],
          "odata.type": "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
        },
        "autoMitigate": false,
        "targetResourceType": "Microsoft.Storage/storageAccounts",
        "targetResourceRegion": "[parameters('global-location')]",
        "actions": [
          {
            "actionGroupId": "[resourceId(parameters('alert-actionGroupResourceGroupName'),'Microsoft.Insights/actionGroups', parameters('alert-actionGroupsName'))]",
          }
        ]
      }
    },
    {
      "condition": "[parameters('alert-enabled')]",
      "type": "microsoft.insights/metricalerts",
      "apiVersion": "2018-03-01",
      "name": "[parameters('storage-SuccessServerLatencyAlertsName')]",
      "location": "global",
      "properties": {
        "description": "要求処理時間の平均超過アラート",
        "severity": 2,
        "enabled": true,
        "scopes": [
          "[resourceId('Microsoft.Storage/storageAccounts', parameters('storage-accountName'))]"
        ],
        "evaluationFrequency": "PT5M",
        "windowSize": "PT5M",
        "criteria": {
          "allOf": [
            {
              "threshold": "[parameters('storage-SuccessServerLatencyAlertsName-threshold')]",
              "name": "Metric1",
              "metricNamespace": "Microsoft.Storage/storageAccounts",
              "metricName": "SuccessServerLatency",
              "operator": "GreaterThan",
              "timeAggregation": "Average",
              "criterionType": "StaticThresholdCriterion"
            }
          ],
          "odata.type": "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
        },
        "autoMitigate": false,
        "targetResourceType": "Microsoft.Storage/storageAccounts",
        "targetResourceRegion": "[parameters('global-location')]",
        "actions": [
          {
            "actionGroupId": "[resourceId(parameters('alert-actionGroupResourceGroupName'),'Microsoft.Insights/actionGroups', parameters('alert-actionGroupsName'))]",
          }
        ]
      }
    },
    {
      "condition": "[parameters('alert-enabled')]",
      "type": "microsoft.insights/activityLogAlerts",
      "apiVersion": "2020-10-01",
      "name": "[parameters('resourceHealth-Alerts')]",
      "location": "Global",
      "properties": {
        "scopes": [
          "[subscription().id]"
        ],
        "condition": {
          "allOf": [
            {
              "field": "category",
              "equals": "ResourceHealth"
            },
            {
              "anyOf": [
                {
                  "field": "properties.cause",
                  "equals": "PlatformInitiated"
                }
              ]
            },
            {
              "anyOf": [
                {
                  "field": "properties.currentHealthStatus",
                  "equals": "Unavailable"
                },
                {
                  "field": "properties.currentHealthStatus",
                  "equals": "Degraded"
                }
              ]
            },
            {
              "anyOf": [
                {
                  "field": "status",
                  "equals": "Active"
                },
                {
                  "field": "status",
                  "equals": "Resolved"
                }
              ]
            },
            {
              "anyOf": [
                {
                  "field": "properties.previousHealthStatus",
                  "equals": "Available"
                }
              ]
            },
            {
              "anyOf": [
                {
                  "field": "resourceGroup",
                  "equals": "[parameters('resourceGroup-Name')]"
                }
              ]
            }
          ]
        },
        "actions": {
          "actionGroups": [
            {
              "actionGroupId": "[resourceId(parameters('alert-actionGroupResourceGroupName'),'Microsoft.Insights/actionGroups', parameters('alert-actionGroupsName'))]",
              "webhookProperties": {}
            }
          ]
        },
        "enabled": true,
        "description": "リソースの正常性チェック"
      }
    }
  ]
}
