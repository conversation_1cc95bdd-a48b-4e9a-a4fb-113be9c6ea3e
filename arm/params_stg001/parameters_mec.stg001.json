{
  "$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentParameters.json#",
  "contentVersion": "*******",
  "parameters": {
    "global-location": {
      "value": "Japan East"
    },
    // =====================
    //
    // Appservice関連パラメータ
    //
    // =====================
    "appServicePlan-ResourceGroup": {
      "value": "rg-attane-stg-share"
    },
    "automation-ResourceGroup":{
      "value": "rg-attane-stg-unique-mec"
    },
    "appServicePlan-hostingPlanName": {
      "value": "plan-attane-stg-001"
    },
    "appService-siteName": {
      "value": "app-attane-stg-mec-001"
    },
    "appService-hostNames": {
      "value": [
        "app-attane-stg-mec-001.azurewebsites.net"
      ]
    },
    "appService-alwaysOn": {
      "value": true
    },
    "appService-env": {
      "value": "{
        \"ASPNETCORE_ENVIRONMENT\": \"Staging\",
        \"AllowedHosts\": \"*\",
        \"AzureAD:Instance\": \"https://login.microsoftonline.com\",
        \"AzureAD:AllowAppIds:0\": \"1fec8e78-bce4-4aaf-ab1b-5451cc387264\",
        \"AzureAD:AllowAppIds:1\": \"5e3ce6c0-2b1f-4285-8d4b-75ee78787346\",
        \"AzureAD:AllowAppIds:2\": \"469c477c-b4cb-42e0-a90c-170f22e2b217\",
        \"AzureAD:Audience\": \"api://fd-attane-stg-001.azurefd.net/2cef218a-59d4-463a-95b6-7973147c70cb\",
        \"AzureAD:ClientId\": \"2cef218a-59d4-463a-95b6-7973147c70cb\",
        \"AzureAD:ClientSecret\": \"****************************************\",
        \"AzureAD:Scopes:Graph\": \"https://graph.microsoft.com/.default\",
        \"AzureAD:Scopes:Spo\": \"https://o365mectest.sharepoint.com/.default\",
        \"AzureAD:Scopes:Api\": \"api://fd-attane-stg-001.azurefd.net/2cef218a-59d4-463a-95b6-7973147c70cb/user_impersonation\",
        \"AzureAD:TenantId\": \"15d6e921-972f-4a4e-81d6-1ec3d73471a5\",
        \"WEBSITE_DNS_SERVER\": \"*************\",
        \"WEBSITE_VNET_ROUTE_ALL\": \"1\",
        \"ServiceBus:ConnectionString\": \"{{TODO: ここに接続文字列を設定する}}\",
        \"Geranium:Search:QueueName\": \"search-process-stg-mec-001\"
      }"
    },
    "appService-healthCheckEnabled": {
      "value": true
    },
    "appService-DiagnosticName":{
      "value": "app-attane-stg-mec-001-diag"
    },
    "waf-frontdoorName": {
      "value": "fd-attane-stg-001"
    },
    "accessLogs-enabled":{
      "value": true
    },
    "waf-enabled": {
      "value": true
    },
    "waf-frontdoorDiagnosticName": {
      "value": "diag-fd-attane-stg-001"
    },
    // =====================
    //
    // Application Insights関連パラメータ
    //
    // =====================
    "appInsights-workspaceName": {
      "value": "log-attane-stg-001"
    },
    "appInsights-componentsName": {
      "value": "appi-attane-stg-001"
    },
    // =====================
    //
    // Storage関連パラメータ
    //
    // =====================
    "storage-accountName": {
      "value": "stattanestgmec001"
    },
    "storage-accessTier": {
      "value": "Cool"
    },
    // =====================
    //
    // Functions関連パラメータ
    //
    // =====================
    "functions-siteName": {
      "value": "func-attane-stg-mec-001"
    },
    "functions-env": {
      "value": "{
        \"FUNCTIONS_EXTENSION_VERSION\": \"~4\",
        \"FUNCTIONS_WORKER_RUNTIME\": \"node\",
        \"WEBSITE_NODE_DEFAULT_VERSION\": \"~18\",
        \"WEBSITE_RUN_FROM_PACKAGE\": \"1\",
        \"AzureClientId\": \"2cef218a-59d4-463a-95b6-7973147c70cb\",
        \"AzureClientSecret\": \"****************************************\",
        \"WEBSITE_TIME_ZONE\": \"Tokyo Standard Time\",
        \"ServiceBus:fullyQualifiedNamespace\": \"sb-attane-stg-001.servicebus.windows.net\",
        \"SearchProcessQueueName\": \"search-process-stg-mec-001\",
        \"AzureFunctionsJobHost__logging__logLevel__Function\": \"Information\"
      },"
    },
    "functions-ipSecurityEnabled" : {
      "value": true
    },
    "functions-healthCheckEnabled" : {
      "value": true
    },
    // =====================
    //
    // automation関連パラメータ
    //
    // =====================
    "automation-enabled": {
      "value": true
    },
    "automation-accountName": {
      "value": "aa-attane-stg-mec-001"
    },
    "automation-runbookName": {
      "value": "Restart-AppService"
    },
    "automation-artifactsLocation": {
      "value": "https://stattanestg001.blob.core.windows.net/"
    },
    "automation-artifactsPath": {
      "value": "sources/Restart.ps1"
    },
    "automation-artifactsLocationSasToken": {
      "value": "?sp=r&st=2023-02-01T07:36:36Z&se=2023-02-01T15:36:36Z&spr=https&sv=2021-06-08&sr=b&sig=U5a7IMnZ9%2BY8GTobZNXMRr%2B6%2FSCc1jikmK7VEIt06J8%3D"
    },
    "automation-scheduleName": {
      "value": "Daily"
    },
    "automation-jobScheduleName": {
      "value": "101F61DD-3F2D-46C7-8D54-0B79D5509865"
    },
    "automation-scheduleStartTime": {
      "value": "2023-02-12T04:00:00+09:00" // 初期作成時、更新時は15分以上未来の日時である必要がある
    },
    "webAppRoleNameGuid-aa": {
      "value": "721EE35A-3D07-4764-9711-F87782AB3712"
    },
    "functionRoleNameGuid-aa": {
      "value": "BFD6AA4F-B7FE-4F33-91AD-4195472FAE01"
    },
    "application-enabled": {
      "value": true
    },
    // =====================
    //
    // ネットワーク関連パラメータ
    //
    // =====================
    "net-vnetEnabled": {
      "value": true
    },
    "vnet-ResourceGroup" : {
      "value" : "rg-attane-stg-share"
    },
    "net-apiEndpointsubnetName": {
      "value": "snet-attane-stg-001"
    },
    "net-vnetName": {
      "value": "vnet-attane-stg-001"
    }
  }
}
