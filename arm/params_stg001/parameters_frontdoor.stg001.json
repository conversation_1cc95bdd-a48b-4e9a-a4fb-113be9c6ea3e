{
  "$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentParameters.json#",
  "contentVersion": "*******",
  "parameters": {
    "global-location": {
      "value": "Japan East"
    },
    // =====================
    //
    // 個社関連パラメータ
    //
    // =====================

    "global-companies": {
      "value": [
        {
          "companyName": "mec",
          "appService-hostName": "app-attane-stg-mec-001.azurewebsites.net",
          "waf-routingName": "routing-attane-stg-mec",
          "waf-customForwardingPath": "/",
          "waf-patternsToMatch": [ "/mec/", "/mec/*" ],
          "waf-backendPoolName": "backend-attane-stg-mec",
          "waf-loadBalancingName": "loadBalancingSettings-stg-mec",
          "waf-healthProbeName": "healthProbeSettings-stg-mec",
          "waf-frontdoorProbeInterval": 255
        }
      ]
    },

    // =====================
    //
    // Web Application Firewall関連パラメータ
    //
    // =====================
    "waf-enabled": {
      "value": true
    },
    "waf-policyName": {
      "value": "wafattanestg001"
    },
    "waf-frontdoorName": {
      "value": "fd-attane-stg-001"
    },
    "waf-frontdoorDiagnosticName": {
      "value": "diag-fd-attane-stg-001"
    },
    "waf-routingName-default": {
      "value": "routing-attane-stg-default"
    },
    // =====================
    //
    // Application Insights関連パラメータ
    //
    // =====================
    "appInsights-workspaceName": {
      "value": "log-attane-stg-001"
    }
  }
}
