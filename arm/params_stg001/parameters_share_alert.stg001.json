{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentParameters.json#", "contentVersion": "*******", "parameters": {"global-location": {"value": "Japan East"}, "resourceGroup-Name": {"value": "rg-attane-stg-share"}, "alert-enabled": {"value": true}, "alert-actionGroupsName": {"value": "ag-attane-stg-001"}, "alert-actionGroupSortName": {"value": "ag-stg-001"}, "alert-emailReceivers": {"value": [{"name": "O365_miTene開発検証用(MJIT)_-EmailAction-", "emailAddress": "<EMAIL>", "useCommonAlertSchema": false}]}, "appInsights-componentsName": {"value": "appi-attane-stg-001"}, "net-publicIpName": {"value": "pip-attane-stg-001"}, "appServicePlan-hostingPlanName": {"value": "plan-attane-stg-001"}, "serviceBus-namespace": {"value": "sb-attane-stg-001"}, "appServicePlan-cpuPercintageAlertsName": {"value": "alert-attane-stg-plan-over_cpu_percentage"}, "appServicePlan-memoryPercentageAlertsName": {"value": "alert-attane-stg-plan-over_memory_percentage"}, "publicIP-ddosAlertsName": {"value": "alert-attane-stg-ddos"}, "serviceBus-queueCountAlertsName": {"value": "alert-attane-stg-queueCount"}, "resourceHealth-Alerts": {"value": "alert-attane-stg-rg-resourcesHealth"}, "appInsights-unhandledExceptionAlertsName": {"value": "alert-attane-stg-unhandledException"}, "resourceHealth-ActivityLogAlerts": {"value": "alert-attane-stg-rg-serviceHealth"}, "appInsights-callerUnmatchAlertsName": {"value": "alert-attane-stg-mec-ase-callerUnmatch"}, "appInsights-tooManyRetryAlertsName": {"value": "alert-attane-stg-mec-ase-tooManyRetry"}, "appInsights-authErrorAlertsName": {"value": "alert-attane-stg-mec-ase-authError"}, "appInsights-missingEnvValuesAlertsName": {"value": "alert-attane-stg-mec-ase-missingEnvValues"}, "appInsights-unautorizedAppAlertsName": {"value": "alert-attane-stg-mec-ase-unautorizedApp"}, "appInsights-apiCallErrorAlertsName": {"value": "alert-attane-stg-mec-ase-apiCallError"}, "appInsights-functionsUnrecoverableErrorAlertsName": {"value": "alert-attane-stg-mec-func-UnrecoverableError"}, "appInsights-functionsRecoverableErrorAlertsName": {"value": "alert-attane-stg-mec-func-RecoverableError"}}}