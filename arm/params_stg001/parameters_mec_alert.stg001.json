{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentParameters.json#", "contentVersion": "1.0.0.0", "parameters": {"global-location": {"value": "Japan East"}, "alert-enabled": {"value": true}, "resourceGroup-Name": {"value": "rg-attane-stg-unique-mec"}, "alert-actionGroupResourceGroupName": {"value": "rg-attane-stg-share"}, "alert-actionGroupsName": {"value": "ag-attane-stg-001"}, "appService-siteName": {"value": "app-attane-stg-001"}, "appService-DataOutAlertsName": {"value": "alert-attane-stg-ase-over_dataOutSize"}, "appService-http4xxAlertsName": {"value": "alert-attane-stg-mec-ase-http4xx"}, "appService-http5xxAlertsName": {"value": "alert-attane-stg-mec-ase-http5xx"}, "resourceHealth-ActivityLogAlerts": {"value": "alert-attane-stg-mec-rg-serviceHealth"}, "resourceHealth-Alerts": {"value": "alert-attane-stg-mec-rg-resourcesHealth"}, "storage-UsedCapacityAlertsName": {"value": "alert-attane-stg-mec-st-over_usedCapacity"}, "storage-accountName": {"value": "stattanestgmec001"}}}