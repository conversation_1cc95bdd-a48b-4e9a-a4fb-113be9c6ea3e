pool:
  name: Azure Pipelines
  vmImage: windows-latest

# CIの実行トリガー
# mainブランチのarm階層以下が更新されたときにだけトリガー
trigger:
  branches:
    include:
      - main
  paths:
    include:
      - arm

steps:
  # Az moduleのインストール
  - powershell: |
      @('Az.Accounts', 'Az.Monitor', 'Az.Resources') | ForEach-Object { if (-not (Get-Module -Name $_)) { Install-Module -Name $_ -AllowClobber -Force } }
    displayName: 'PowerShell Script:  Install AZ modules'
  # ARMテンプレートをデプロイ
  - task: PowerShell@2
    displayName: 'PowerShell Script: Deploy ARM Template'
    inputs:
      targetType: filePath
      filePath: '$(System.DefaultWorkingDirectory)/arm/deploy_resourcegroup.ps1'
      arguments: '-Env $(ENV) -Company $(Company)'
