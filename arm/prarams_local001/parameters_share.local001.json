{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentParameters.json#", "contentVersion": "*******", "parameters": {"global-location": {"value": "Japan East"}, "appServicePlan-hostingPlanName": {"value": "plan-attane-local1-001"}, "appServicePlan-workerSize": {"value": "0"}, "appServicePlan-workerSizeId": {"value": "0"}, "appServicePlan-numberOfWorkers": {"value": "1"}, "appServicePlan-maximumElasticWorkerCount": {"value": "2"}, "appServicePlan-sku": {"value": "Basic"}, "appServicePlan-skuCode": {"value": "B2"}, "appServicePlan-capacity": {"value": "2"}, "appInsights-workspaceName": {"value": "log-attane-local1-001"}, "appInsights-workspaceType": {"value": "pergb2018"}, "appInsights-componentsName": {"value": "appi-attane-local1-001"}, "appInsights-retentionInDays": {"value": 30}, "autoScale-enabled": {"value": false}, "appServicePlan-autoScale": {"value": "plan-attane-local1-001"}, "application-enabled": {"value": false}, "serviceBus-namespace": {"value": "sb-attane-local1-001"}, "serviceBus-skuName": {"value": "Standard"}, "net-vnetEnabled": {"value": false}, "net-vnetName": {"value": "vnet-attane-local1-001"}, "net-vnet-addressPrefixes": {"value": "***********/16"}, "net-apiEndpointsubnetName": {"value": "snet-attane-local1-001"}, "net-subnet-addressPrefixes": {"value": "***********/24"}, "net-natGwName": {"value": "ng-attane-local1-001"}, "net-publicIpName": {"value": "pip-attane-local1-001"}}}