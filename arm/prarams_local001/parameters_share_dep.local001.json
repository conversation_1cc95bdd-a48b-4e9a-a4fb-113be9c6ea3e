{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentParameters.json#", "contentVersion": "1.0.0.0", "parameters": {"serviceBus-iamEnabled": {"value": true}, "serviceBus-namespace": {"value": "sb-attane-local1-001"}, "site-params": {"value": [{"resourceGroup": "rg-geranium-attane-local001", "queueName": "search-process", "appServiceSiteName": "app-attane-local1-001", "functionsSiteName": "func-attane-local1-001", "appServiceServiceBusRoleName": "3E7F0FDB-D113-48AC-B0BA-F46D2AF65B8B", "functionsServiceBusRoleName": "CA0E226E-2F3C-49D8-8268-AB326EDB031C", "appServiceQueueSenderRoleName": "8A8977ED-6D86-40AC-893A-212AA30900E3", "functionsQueueSenderRoleName": "88745F26-44FB-4C2E-A4E1-11DE1560C4E3", "functionsQueueReceiverRoleName": "AAC4DCCE-B835-42D0-B864-BF49D06A01C4"}]}}}