{
  "$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentParameters.json#",
  "contentVersion": "*******",
  "parameters": {
    "global-location": {
      "value": "Japan East"
    },
    "appServicePlan-ResourceGroup": {
      "value": "rg-geranium-attane-local001"
    },
    "automation-ResourceGroup":{
      "value": "rg-geranium-attane-local001"
    },
    "appServicePlan-hostingPlanName": {
      "value": "plan-attane-local1-001"
    },
    "appService-siteName": {
      "value": "app-attane-local1-001"
    },
    "appService-hostNames": {
      "value": [
        "app-attane-local1-001.azurewebsites.net"
      ]
    },
    "appService-alwaysOn": {
      "value": true
    },
    "appService-env": {
      "value": "{
        \"ASPNETCORE_ENVIRONMENT\": \"AppDevelopment\",
        \"AzureAD:AllowAppIds:0\": \"1fec8e78-bce4-4aaf-ab1b-5451cc387264\",
        \"AzureAD:AllowAppIds:1\": \"5e3ce6c0-2b1f-4285-8d4b-75ee78787346\",
        \"AzureAD:AllowAppIds:2\": \"469c477c-b4cb-42e0-a90c-170f22e2b217\",
        \"AzureAD:Audience\": \"api://localhost:3000/6b399905-b6c1-4abe-a15c-e694024d9907\",
        \"AzureAD:ClientId\": \"6b399905-b6c1-4abe-a15c-e694024d9907\",
        \"AzureAD:ClientSecret\": \"****************************************\",
        \"AzureAD:Scopes:Graph\": \"https://graph.microsoft.com/.default\",
        \"AzureAD:Scopes:Spo\": \"https://ydxxq.sharepoint.com/.default\",
        \"AzureAD:Scopes:Api\": \"api://localhost:3000/6b399905-b6c1-4abe-a15c-e694024d9907/user_impersonation\",
        \"AzureAD:TenantId\": \"52fc0f11-0c64-40d6-b802-3b80cbef8e3f\",
        \"WEBSITE_DNS_SERVER\": \"*************\",
        \"WEBSITE_VNET_ROUTE_ALL\": \"1\"
      }"
    },
    "appService-healthCheckEnabled": {
      "value": false
    },
    "appService-DiagnosticName":{
      "value": "app-attane-local1-001-diag"
    },
    "waf-frontdoorName": {
      "value": "fd-attane-local1-001"
    },
    "accessLogs-enabled":{
      "value": false
    },
    "waf-enabled": {
      "value": false
    },
    "waf-frontdoorDiagnosticName": {
      "value": "diag-fd-attane-local1-001"
    },
    "appInsights-workspaceName": {
      "value": "log-attane-local1-001"
    },
    "appInsights-componentsName": {
      "value": "appi-attane-local1-001"
    },
    "storage-accountName": {
      "value": "stattanelocal1001"
    },
    "storage-accessTier": {
      "value": "Cool"
    },
    "functions-siteName": {
      "value": "func-attane-local1-001"
    },
    "functions-env": {
      "value": "{
        \"FUNCTIONS_EXTENSION_VERSION\": \"~4\",
        \"FUNCTIONS_WORKER_RUNTIME\": \"node\",
        \"WEBSITE_NODE_DEFAULT_VERSION\": \"~18\",
        \"WEBSITE_RUN_FROM_PACKAGE\": \"1\",
        \"AzureClientId\": \"6b399905-b6c1-4abe-a15c-e694024d9907\",
        \"AzureClientSecret\": \"****************************************\",
        \"AzureTenantId\": \"52fc0f11-0c64-40d6-b802-3b80cbef8e3f\",
        \"WEBSITE_TIME_ZONE\": \"Tokyo Standard Time\",
        \"AzureFunctionsJobHost__logging__logLevel__Function\": \"Trace\"
    }"
    },
    "functions-ipSecurityEnabled" : {
      "value": false
    },
    "functions-healthCheckEnabled" : {
      "value": false
    },
    "automation-enabled": {
      "value": false
    },
    "automation-accountName": {
      "value": "aa-attane-local1-001"
    },
    "automation-runbookName": {
      "value": "Restart-AppService"
    },
    "automation-artifactsLocation": {
      "value": "https://stattanelocal1001.blob.core.windows.net/"
    },
    "automation-artifactsPath": {
      "value": "artifacts/Restart.ps1"
    },
    "automation-artifactsLocationSasToken": {
      "value": "?sp=r&st=2023-01-13T03:02:37Z&se=2023-01-13T11:02:37Z&spr=https&sv=2021-06-08&sr=b&sig=35RFWRhJr4kPkZ9Pwzky3nDJQajaMONOHQxmyNjJ57U%3D"
    },
    "automation-scheduleName": {
      "value": "Daily"
    },
    "automation-jobScheduleName": {
      "value": "97F53B25-3CE1-4650-84C4-96B20F312EBE"
    },
    //"automation-scheduleStartTime": {
    //  "value": "2022-07-19T13:00:00+09:00" // 初期作成時、更新時は15分以上未来の日時である必要がある
    //},
    "webAppRoleNameGuid": {
      "value": "C796D625-4346-4FFE-AC8E-9B4AFF0C1B93" //使わないのでDev002と同じ値
    },
    "functionRoleNameGuid": {
      "value": "45D18F6A-1334-485B-8C1D-437D2E932194" //使わないのでDev002と同じ値
    },
    "net-vnetEnabled": {
      "value": false
    },
    "vnet-ResourceGroup" : {
      "value" : "rg-geranium-attane-local001"
    },
    "net-apiEndpointsubnetName": {
      "value": "snet-attane-local1-001"
    },
    "application-enabled": {
      "value": false
    },
    "net-vnetName": {
      "value": "vnet-attane-local1-001"
    },
    "net-vnetIpRules": {
      "value": [
        {
          "value": "**************",
          "action": "Allow"
        },
        {
          "value": "**************",
          "action": "Allow"
        },
        {
          "value": "************",
          "action": "Allow"
        },
        {
          "value": "*********",
          "action": "Allow"
        },
        {
            "value": "*************",
            "action": "Allow"
        },
        {
            "value": "*************",
            "action": "Allow"
        },
        {
            "value": "*************",
            "action": "Allow"
        },
        {
            "value": "************",
            "action": "Allow"
        },
        {
            "value": "**************",
            "action": "Allow"
        },
        {
            "value": "*************",
            "action": "Allow"
        },
        {
            "value": "************",
            "action": "Allow"
        },
        {
            "value": "184.72.47.199",
            "action": "Allow"
        },
        {
            "value": "54.193.251.180",
            "action": "Allow"
        },
        {
            "value": "54.241.31.130",
            "action": "Allow"
        },
        {
            "value": "13.52.27.189",
            "action": "Allow"
        },
        {
            "value": "13.52.105.217",
            "action": "Allow"
        },
        {
            "value": "13.52.157.154",
            "action": "Allow"
        },
        {
            "value": "13.52.175.228",
            "action": "Allow"
        },
        {
            "value": "52.52.50.152",
            "action": "Allow"
        },
        {
            "value": "52.52.110.223",
            "action": "Allow"
        },
        {
            "value": "50.18.117.136",
            "action": "Allow"
        },
        {
            "value": "54.215.44.246",
            "action": "Allow"
        }
      ]
    }
  }
}
