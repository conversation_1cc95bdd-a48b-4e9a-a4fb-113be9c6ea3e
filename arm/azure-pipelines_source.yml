pool:
  name: Azure Pipelines
  vmImage: windows-latest

# CIの実行トリガー：なし
trigger: none  

steps:
  # Az moduleのインストール
  - powershell: |
      @('Az.Accounts', 'Az.Monitor', 'Az.Resources') | ForEach-Object { if (-not (Get-Module -Name $_)) { Install-Module -Name $_ -AllowClobber -Force } }
    displayName: 'PowerShell Script:  Install AZ modules'

  # ARMテンプレートをデプロイ
  - task: PowerShell@2
    displayName: 'PowerShell Script: Deploy ARM Template'
    inputs:
      targetType: filePath
      filePath: '$(System.DefaultWorkingDirectory)/arm/deploy_rg_sources.ps1'
      arguments: '-Env $(ENV)'
