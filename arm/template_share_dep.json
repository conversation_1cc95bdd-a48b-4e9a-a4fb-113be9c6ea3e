{
  "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
  "contentVersion": "1.0.0.0",
  "parameters": {
    "serviceBus-iamEnabled": {
      "type": "bool"
    },
    "serviceBus-namespace": {
      "type": "string"
    },
    "site-params": {
      "type": "array"
    }
  },
  "variables": {
  },
  "functions": [],
  "resources": [
    // =====================
    //
    // Service BusのQueue
    //
    // =====================
    {
      "copy": {
        "name": "siteParam",
        "count": "[length(parameters('site-params'))]"
      },
      "type": "Microsoft.ServiceBus/namespaces/queues",
      "apiVersion": "2022-10-01-preview",
      "name": "[concat(parameters('serviceBus-namespace'), '/', parameters('site-params')[copyIndex('siteParam')].queueName)]",
      "location": "japaneast",
      "properties": {
        "lockDuration": "PT3M",
        "requiresDuplicateDetection": false,
        "requiresSession": false,
        "defaultMessageTimeToLive": "P14D",
        "deadLetteringOnMessageExpiration": false,
        "enableBatchedOperations": true,
        "maxDeliveryCount": 10,
        "status": "Active",
        "autoDeleteOnIdle": "P10675199DT2H48M5.4775807S",
        "enablePartitioning": false,
        "enableExpress": false
      }
    },
    // =====================
    //
    // Service BusのIAM追加
    //
    // =====================
    {
      "condition": "[parameters('serviceBus-iamEnabled')]",
      "copy": {
        "name": "siteParam",
        "count": "[length(parameters('site-params'))]"
      },
      "type": "Microsoft.Authorization/roleAssignments",
      "apiVersion": "2022-01-01-preview",
      "name": "[parameters('site-params')[copyIndex('siteParam')].appServiceServiceBusRoleName]",
      "scope": "[resourceId('Microsoft.ServiceBus/namespaces', parameters('serviceBus-namespace'))]",
      "properties": {
        "roleDefinitionId": "[concat('/subscriptions/', subscription().subscriptionId, '/providers/Microsoft.Authorization/roleDefinitions/', 'acdd72a7-3385-48ef-bd42-f606fba81ae7')]",
        "principalId": "[reference(resourceId(parameters('site-params')[copyIndex('siteParam')].resourceGroup, 'Microsoft.Web/sites', parameters('site-params')[copyIndex('siteParam')].appServiceSiteName), '2022-03-01', 'Full').identity.principalId]"
      }
    },
    {
      "condition": "[parameters('serviceBus-iamEnabled')]",
      "copy": {
        "name": "siteParam",
        "count": "[length(parameters('site-params'))]"
      },
      "type": "Microsoft.Authorization/roleAssignments",
      "apiVersion": "2022-01-01-preview",
      "name": "[parameters('site-params')[copyIndex('siteParam')].functionsServiceBusRoleName]",
      "scope": "[resourceId('Microsoft.ServiceBus/namespaces', parameters('serviceBus-namespace'))]",
      "properties": {
        "roleDefinitionId": "[concat('/subscriptions/', subscription().subscriptionId, '/providers/Microsoft.Authorization/roleDefinitions/', 'acdd72a7-3385-48ef-bd42-f606fba81ae7')]",
        "principalId": "[reference(resourceId(parameters('site-params')[copyIndex('siteParam')].resourceGroup, 'Microsoft.Web/sites', parameters('site-params')[copyIndex('siteParam')].functionsSiteName), '2022-03-01', 'Full').identity.principalId]"
      }
    },
    {
      "condition": "[parameters('serviceBus-iamEnabled')]",
      "copy": {
        "name": "siteParam",
        "count": "[length(parameters('site-params'))]"
      },
      "dependsOn": [
        "[resourceId('Microsoft.ServiceBus/namespaces/queues', parameters('serviceBus-namespace'), parameters('site-params')[copyIndex('siteParam')].queueName)]"
      ],
      "type": "Microsoft.Authorization/roleAssignments",
      "apiVersion": "2022-01-01-preview",
      "name": "[parameters('site-params')[copyIndex('siteParam')].appServiceQueueSenderRoleName]",
      "scope": "[concat(resourceId('Microsoft.ServiceBus/namespaces', parameters('serviceBus-namespace')), '/queues/', parameters('site-params')[copyIndex('siteParam')].queueName)]",
      "properties": {
        "roleDefinitionId": "[concat('/subscriptions/', subscription().subscriptionId, '/providers/Microsoft.Authorization/roleDefinitions/', '69a216fc-b8fb-44d8-bc22-1f3c2cd27a39')]",
        "principalId": "[reference(resourceId(parameters('site-params')[copyIndex('siteParam')].resourceGroup, 'Microsoft.Web/sites', parameters('site-params')[copyIndex('siteParam')].appServiceSiteName), '2022-03-01', 'Full').identity.principalId]"
      }
    },
    {
      "condition": "[parameters('serviceBus-iamEnabled')]",
      "copy": {
        "name": "siteParam",
        "count": "[length(parameters('site-params'))]"
      },
      "dependsOn": [
        "[resourceId('Microsoft.ServiceBus/namespaces/queues', parameters('serviceBus-namespace'), parameters('site-params')[copyIndex('siteParam')].queueName)]"
      ],
      "type": "Microsoft.Authorization/roleAssignments",
      "apiVersion": "2022-01-01-preview",
      "name": "[parameters('site-params')[copyIndex('siteParam')].functionsQueueSenderRoleName]",
      "scope": "[concat(resourceId('Microsoft.ServiceBus/namespaces', parameters('serviceBus-namespace')), '/queues/', parameters('site-params')[copyIndex('siteParam')].queueName)]",
      "properties": {
        "roleDefinitionId": "[concat('/subscriptions/', subscription().subscriptionId, '/providers/Microsoft.Authorization/roleDefinitions/', '69a216fc-b8fb-44d8-bc22-1f3c2cd27a39')]",
        "principalId": "[reference(resourceId(parameters('site-params')[copyIndex('siteParam')].resourceGroup, 'Microsoft.Web/sites', parameters('site-params')[copyIndex('siteParam')].functionsSiteName),'2022-03-01','Full').identity.principalId]"
      }
    },
    {
      "condition": "[parameters('serviceBus-iamEnabled')]",
      "copy": {
        "name": "siteParam",
        "count": "[length(parameters('site-params'))]"
      },
      "dependsOn": [
        "[resourceId('Microsoft.ServiceBus/namespaces/queues', parameters('serviceBus-namespace'), parameters('site-params')[copyIndex('siteParam')].queueName)]"
      ],
      "type": "Microsoft.Authorization/roleAssignments",
      "apiVersion": "2022-01-01-preview",
      "name": "[parameters('site-params')[copyIndex('siteParam')].functionsQueueReceiverRoleName]",
      "scope": "[concat(resourceId('Microsoft.ServiceBus/namespaces', parameters('serviceBus-namespace')), '/queues/', parameters('site-params')[copyIndex('siteParam')].queueName)]",
      "properties": {
        "roleDefinitionId": "[concat('/subscriptions/', subscription().subscriptionId, '/providers/Microsoft.Authorization/roleDefinitions/', '4f6d3b9b-027b-4f4c-9142-0e5a2a2247e0')]",
        "principalId": "[reference(resourceId(parameters('site-params')[copyIndex('siteParam')].resourceGroup, 'Microsoft.Web/sites', parameters('site-params')[copyIndex('siteParam')].functionsSiteName),'2022-03-01','Full').identity.principalId]"
      }
    }
  ],
  "outputs": {
  }
}
