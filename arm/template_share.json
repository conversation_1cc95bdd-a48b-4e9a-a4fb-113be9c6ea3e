{
  "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
  "contentVersion": "*******",
  "parameters": {
    "global-location": {
      "type": "string"
    },
    // =====================
    //
    // AppservicePlan関連パラメータ
    //
    // =====================
    "application-enabled": {
      "type": "bool"
    },
    "appServicePlan-hostingPlanName": {
      "type": "string"
    },
    "appServicePlan-workerSize": {
      "type": "string"
    },
    "appServicePlan-workerSizeId": {
      "type": "string"
    },
    "appServicePlan-numberOfWorkers": {
      "type": "string"
    },
    "appServicePlan-maximumElasticWorkerCount": {
      "type": "string"
    },
    "appServicePlan-sku": {
      "type": "string"
    },
    "appServicePlan-skuCode": {
      "type": "string"
    },
    "appServicePlan-capacity": {
      "type": "string"
    },
    "autoScale-enabled": {
      "type": "bool"
    },
    "appServicePlan-autoScale": {
      "type": "string"
    },
    // =====================
    //
    // Application Insights関連パラメータ
    //
    // =====================
    "appInsights-workspaceName": {
      "type": "string"
    },
    "appInsights-workspaceType": {
      "type": "string"
    },
    "appInsights-componentsName": {
      "type": "string"
    },
    "appInsights-retentionInDays": {
      "type": "int"
    },
    // =====================
    //
    // サービスバス関連パラメータ
    //
    // =====================
    "serviceBus-namespace": {
      "type": "string"
    },
    "serviceBus-skuName": {
      "type": "string"
    },
    // =====================
    //
    // ネットワーク関連パラメータ
    //
    // =====================
    "net-vnetEnabled": {
      "type": "bool"
    },
    "net-vnetName": {
      "type": "string"
    },
    "net-vnet-addressPrefixes": {
      "type": "string"
    },
    "net-apiEndpointsubnetName": {
      "type": "string"
    },
    "net-subnet-addressPrefixes": {
      "type": "string"
    },
    "net-natGwName": {
      "type": "string"
    },
    "net-publicIpName": {
      "type": "string"
    }
  },
  "variables": {
  },
  "functions": [],
  "resources": [
    // =====================
    //
    // AppservicePlan関連リソース
    //
    // =====================
    {
      "condition": "[parameters('application-enabled')]",
      "apiVersion": "2021-02-01",
      "name": "[parameters('appServicePlan-hostingPlanName')]",
      "type": "Microsoft.Web/serverfarms",
      "location": "[parameters('global-location')]",
      "kind": "app",
      "tags": {},
      "dependsOn": [],
      "properties": {
        "name": "[parameters('appServicePlan-hostingPlanName')]",
        "workerSize": "[parameters('appServicePlan-workerSize')]",
        "workerSizeId": "[parameters('appServicePlan-workerSizeId')]",
        "numberOfWorkers": "[parameters('appServicePlan-numberOfWorkers')]",
        "maximumElasticWorkerCount": "[parameters('appServicePlan-maximumElasticWorkerCount')]"
      },
      "sku": {
        "Tier": "[parameters('appServicePlan-sku')]",
        "Name": "[parameters('appServicePlan-skuCode')]",
        "Capacity": "[parameters('appServicePlan-capacity')]"
      }
    },

    /**

    {
      "condition": "[parameters('application-enabled')]",
      "location": "[parameters('global-location')]",
      "apiVersion": "2021-05-01-preview",
      "name": "[parameters('appServicePlan-autoScale')]",
      "type": "Microsoft.Insights/autoscaleSettings",
      "tags": {},
      "dependsOn": [
        "[resourceId('Microsoft.Web/serverfarms', parameters('appServicePlan-hostingPlanName'))]"
      ],
      "properties": {
        "name": "[parameters('appServicePlan-autoScale')]",
        "enabled": "[parameters('autoScale-enabled')]",
        "targetResourceUri": "[resourceId('Microsoft.Web/serverfarms', parameters('appServicePlan-hostingPlanName'))]",
        "profiles": [
          {
            "name": "Scale out weekday noon",
            "capacity": {
              "minimum": "3",
              "maximum": "3",
              "default": "3"
            },
            "rules": [],
            "recurrence": {
              "frequency": "Week",
              "schedule": {
                "timeZone": "Tokyo Standard Time",
                "days": [
                  "Monday",
                  "Tuesday",
                  "Wednesday",
                  "Thursday",
                  "Friday"
                ],
                "hours": [
                  11
                ],
                "minutes": [
                  30
                ]
              }
            }
          },
          {
            "name": "Scale out weekday evening",
            "capacity": {
              "minimum": "4",
              "maximum": "4",
              "default": "4"
            },
            "rules": [],
            "recurrence": {
              "frequency": "Week",
              "schedule": {
                "timeZone": "Tokyo Standard Time",
                "days": [
                  "Monday",
                  "Tuesday",
                  "Wednesday",
                  "Thursday",
                  "Friday"
                ],
                "hours": [
                  16
                ],
                "minutes": [
                  30
                ]
              }
            }
          },
          {
            "name": "Scale in weekday 1",
            "capacity": {
              "minimum": "1",
              "maximum": "1",
              "default": "1"
            },
            "rules": [],
            "recurrence": {
              "frequency": "Week",
              "schedule": {
                "timeZone": "Tokyo Standard Time",
                "days": [
                  "Monday",
                  "Tuesday",
                  "Wednesday",
                  "Thursday",
                  "Friday"
                ],
                "hours": [
                  19
                ],
                "minutes": [
                  30
                ]
              }
            }
          },
          {
            "name": "Scale in weekday 2",
            "capacity": {
              "minimum": "1",
              "maximum": "1",
              "default": "1"
            },
            "rules": [],
            "recurrence": {
              "frequency": "Week",
              "schedule": {
                "timeZone": "Tokyo Standard Time",
                "days": [
                  "Monday",
                  "Tuesday",
                  "Wednesday",
                  "Thursday",
                  "Friday"
                ],
                "hours": [
                  0
                ],
                "minutes": [
                  0
                ]
              }
            }
          },
          {
            "name": "Scale in weekend",
            "capacity": {
              "minimum": "1",
              "maximum": "1",
              "default": "1"
            },
            "rules": [],
            "recurrence": {
              "frequency": "Week",
              "schedule": {
                "timeZone": "Tokyo Standard Time",
                "days": [
                  "Saturday",
                  "Sunday"
                ],
                "hours": [
                  0
                ],
                "minutes": [
                  0
                ]
              }
            }
          },
          {
            "name": "{\"name\":\"Default scale\",\"for\":\"Scale out weekday noon\"}",
            "capacity": {
              "minimum": "2",
              "maximum": "2",
              "default": "2"
            },
            "rules": [],
            "recurrence": {
              "frequency": "Week",
              "schedule": {
                "timeZone": "Tokyo Standard Time",
                "days": [
                  "Monday",
                  "Tuesday",
                  "Wednesday",
                  "Thursday",
                  "Friday"
                ],
                "hours": [
                  12
                ],
                "minutes": [
                  30
                ]
              }
            }
          },
          {
            "name": "{\"name\":\"Default scale\",\"for\":\"Scale out weekday evening\"}",
            "capacity": {
              "minimum": "2",
              "maximum": "2",
              "default": "2"
            },
            "rules": [],
            "recurrence": {
              "frequency": "Week",
              "schedule": {
                "timeZone": "Tokyo Standard Time",
                "days": [
                  "Monday",
                  "Tuesday",
                  "Wednesday",
                  "Thursday",
                  "Friday"
                ],
                "hours": [
                  17
                ],
                "minutes": [
                  30
                ]
              }
            }
          },
          {
            "name": "{\"name\":\"Default scale\",\"for\":\"Scale in weekday 2\"}",
            "capacity": {
              "minimum": "2",
              "maximum": "2",
              "default": "2"
            },
            "rules": [],
            "recurrence": {
              "frequency": "Week",
              "schedule": {
                "timeZone": "Tokyo Standard Time",
                "days": [
                  "Monday",
                  "Tuesday",
                  "Wednesday",
                  "Thursday",
                  "Friday"
                ],
                "hours": [
                  6
                ],
                "minutes": [
                  59
                ]
              }
            }
          }
        ],
        "notifications": [],
        "targetResourceLocation": "[parameters('global-location')]"
      }
    },

    **/

    // =====================
    //
    // Application Insights 関連リソース
    //
    // =====================
    {
      "apiVersion": "2020-10-01",
      "name": "[parameters('appInsights-workspaceName')]",
      "type": "Microsoft.OperationalInsights/workspaces",
      "location": "[parameters('global-location')]",
      "properties": {
        "sku": {
          "name": "[parameters('appInsights-workspaceType')]"
        },
        "retentionInDays": "[parameters('appInsights-retentionInDays')]",
        "features": {
          "legacy": 0,
          "searchVersion": 1,
          "enableLogAccessUsingOnlyResourcePermissions": true
        },
        "workspaceCapping": {
          "dailyQuotaGb": -1
        },
        "publicNetworkAccessForIngestion": "Enabled",
        "publicNetworkAccessForQuery": "Enabled"
      }
    },
    {
      "apiVersion": "2020-02-02",
      "type": "microsoft.insights/components",
      "name": "[parameters('appInsights-componentsName')]",
      "location": "[parameters('global-location')]",
      "dependsOn": [
        "[resourceId('Microsoft.OperationalInsights/workspaces', parameters('appInsights-workspaceName'))]"
      ],
      "kind": "web",
      "properties": {
        "Application_Type": "web",
        "Flow_Type": "Redfield",
        "Request_Source": "IbizaAIExtension",
        "WorkspaceResourceId": "[resourceId('Microsoft.OperationalInsights/workspaces', parameters('appInsights-workspaceName'))]",
        "IngestionMode": "LogAnalytics",
        "publicNetworkAccessForIngestion": "Enabled",
        "publicNetworkAccessForQuery": "Enabled",
        "DisableIpMasking": false,
        "ProactiveDetectionConfigs": [
          {
            "name": "degradationindependencyduration",
            "properties": {
              "ruleDefinitions": {
                "Name": "degradationindependencyduration",
                "DisplayName": "Degradation in dependency duration",
                "Description": "Smart Detection rules notify you of performance anomaly issues.",
                "HelpUrl": "https://docs.microsoft.com/en-us/azure/application-insights/app-insights-proactive-performance-diagnostics",
                "IsHidden": false,
                "IsEnabledByDefault": true,
                "IsInPreview": false,
                "SupportsEmailNotifications": true
              },
              "enabled": true,
              "sendEmailsToSubscriptionOwners": true,
              "customEmails": []
            }
          },
          {
            "name": "degradationinserverresponsetime",
            "properties": {
              "ruleDefinitions": {
                "Name": "degradationinserverresponsetime",
                "DisplayName": "Degradation in server response time",
                "Description": "Smart Detection rules notify you of performance anomaly issues.",
                "HelpUrl": "https://docs.microsoft.com/en-us/azure/application-insights/app-insights-proactive-performance-diagnostics",
                "IsHidden": false,
                "IsEnabledByDefault": true,
                "IsInPreview": false,
                "SupportsEmailNotifications": true
              },
              "name": "degradationinserverresponsetime",
              "enabled": true,
              "sendEmailsToSubscriptionOwners": true,
              "customEmails": []
            }
          },
          {
            "name": "digestMailConfiguration",
            "properties": {
              "ruleDefinitions": {
                "Name": "digestMailConfiguration",
                "DisplayName": "Digest Mail Configuration",
                "Description": "This rule describes the digest mail preferences",
                "HelpUrl": "www.homail.com",
                "IsHidden": true,
                "IsEnabledByDefault": true,
                "IsInPreview": false,
                "SupportsEmailNotifications": true
              },
              "name": "digestMailConfiguration",
              "enabled": true,
              "sendEmailsToSubscriptionOwners": true,
              "customEmails": []
            }
          },
          {
            "name": "extension_billingdatavolumedailyspikeextension",
            "properties": {
              "ruleDefinitions": {
                "Name": "extension_billingdatavolumedailyspikeextension",
                "DisplayName": "Abnormal rise in daily data volume (preview)",
                "Description": "This detection rule automatically analyzes the billing data generated by your application, and can warn you about an unusual increase in your application's billing costs",
                "HelpUrl": "https://github.com/Microsoft/ApplicationInsights-Home/tree/master/SmartDetection/billing-data-volume-daily-spike.md",
                "IsHidden": false,
                "IsEnabledByDefault": true,
                "IsInPreview": true,
                "SupportsEmailNotifications": false
              },
              "name": "extension_billingdatavolumedailyspikeextension",
              "enabled": true,
              "sendEmailsToSubscriptionOwners": true,
              "customEmails": []
            }
          },
          {
            "name": "extension_canaryextension",
            "properties": {
              "ruleDefinitions": {
                "Name": "extension_canaryextension",
                "DisplayName": "Canary extension",
                "Description": "Canary extension",
                "HelpUrl": "https://github.com/Microsoft/ApplicationInsights-Home/blob/master/SmartDetection/",
                "IsHidden": true,
                "IsEnabledByDefault": true,
                "IsInPreview": true,
                "SupportsEmailNotifications": false
              },
              "name": "extension_canaryextension",
              "enabled": true,
              "sendEmailsToSubscriptionOwners": true,
              "customEmails": []
            }
          },
          {
            "name": "extension_exceptionchangeextension",
            "dependsOn": [
              "[resourceId('microsoft.insights/components', parameters('appInsights-componentsName'))]",
              "[resourceId('Microsoft.OperationalInsights/workspaces', parameters('appInsights-workspaceName'))]",
              "[resourceId('microsoft.insights/components/ProactiveDetectionConfigs', parameters('appInsights-componentsName'), 'extension_canaryextension')]"
            ],
            "properties": {
              "ruleDefinitions": {
                "Name": "extension_exceptionchangeextension",
                "DisplayName": "Abnormal rise in exception volume (preview)",
                "Description": "This detection rule automatically analyzes the exceptions thrown in your application, and can warn you about unusual patterns in your exception telemetry.",
                "HelpUrl": "https://github.com/Microsoft/ApplicationInsights-Home/blob/master/SmartDetection/abnormal-rise-in-exception-volume.md",
                "IsHidden": false,
                "IsEnabledByDefault": true,
                "IsInPreview": true,
                "SupportsEmailNotifications": false
              },
              "name": "extension_exceptionchangeextension",
              "enabled": true,
              "sendEmailsToSubscriptionOwners": true,
              "customEmails": []
            }
          },
          {
            "name": "extension_memoryleakextension",
            "properties": {
              "ruleDefinitions": {
                "Name": "extension_memoryleakextension",
                "DisplayName": "Potential memory leak detected (preview)",
                "Description": "This detection rule automatically analyzes the memory consumption of each process in your application, and can warn you about potential memory leaks or increased memory consumption.",
                "HelpUrl": "https://github.com/Microsoft/ApplicationInsights-Home/tree/master/SmartDetection/memory-leak.md",
                "IsHidden": false,
                "IsEnabledByDefault": true,
                "IsInPreview": true,
                "SupportsEmailNotifications": false
              },
              "name": "extension_memoryleakextension",
              "enabled": true,
              "sendEmailsToSubscriptionOwners": true,
              "customEmails": []
            }
          },
          {
            "name": "extension_securityextensionspackage",
            "properties": {
              "ruleDefinitions": {
                "Name": "extension_securityextensionspackage",
                "DisplayName": "Potential security issue detected (preview)",
                "Description": "This detection rule automatically analyzes the telemetry generated by your application and detects potential security issues.",
                "HelpUrl": "https://github.com/Microsoft/ApplicationInsights-Home/blob/master/SmartDetection/application-security-detection-pack.md",
                "IsHidden": false,
                "IsEnabledByDefault": true,
                "IsInPreview": true,
                "SupportsEmailNotifications": false
              },
              "name": "extension_securityextensionspackage",
              "enabled": true,
              "sendEmailsToSubscriptionOwners": true,
              "customEmails": []
            }
          },
          {
            "name": "extension_traceseveritydetector",
            "properties": {
              "ruleDefinitions": {
                "Name": "extension_traceseveritydetector",
                "DisplayName": "Degradation in trace severity ratio (preview)",
                "Description": "This detection rule automatically analyzes the trace logs emitted from your application, and can warn you about unusual patterns in the severity of your trace telemetry.",
                "HelpUrl": "https://github.com/Microsoft/ApplicationInsights-Home/blob/master/SmartDetection/degradation-in-trace-severity-ratio.md",
                "IsHidden": false,
                "IsEnabledByDefault": true,
                "IsInPreview": true,
                "SupportsEmailNotifications": false
              },
              "name": "extension_traceseveritydetector",
              "enabled": true,
              "sendEmailsToSubscriptionOwners": true,
              "customEmails": []
            }
          },
          {
            "name": "longdependencyduration",
            "properties": {
              "ruleDefinitions": {
                "Name": "longdependencyduration",
                "DisplayName": "Long dependency duration",
                "Description": "Smart Detection rules notify you of performance anomaly issues.",
                "HelpUrl": "https://docs.microsoft.com/en-us/azure/application-insights/app-insights-proactive-performance-diagnostics",
                "IsHidden": false,
                "IsEnabledByDefault": true,
                "IsInPreview": false,
                "SupportsEmailNotifications": true
              },
              "name": "longdependencyduration",
              "enabled": true,
              "sendEmailsToSubscriptionOwners": true,
              "customEmails": []
            }
          },
          {
            "name": "migrationToAlertRulesCompleted",
            "properties": {
              "ruleDefinitions": {
                "Name": "migrationToAlertRulesCompleted",
                "DisplayName": "Migration To Alert Rules Completed",
                "Description": "A configuration that controls the migration state of Smart Detection to Smart Alerts",
                "HelpUrl": "https://docs.microsoft.com/en-us/azure/application-insights/app-insights-proactive-performance-diagnostics",
                "IsHidden": true,
                "IsEnabledByDefault": false,
                "IsInPreview": true,
                "SupportsEmailNotifications": false
              },
              "name": "migrationToAlertRulesCompleted",
              "enabled": false,
              "sendEmailsToSubscriptionOwners": true,
              "customEmails": []
            }
          },
          {
            "name": "slowpageloadtime')]",
            "properties": {
              "ruleDefinitions": {
                "Name": "slowpageloadtime",
                "DisplayName": "Slow page load time",
                "Description": "Smart Detection rules notify you of performance anomaly issues.",
                "HelpUrl": "https://docs.microsoft.com/en-us/azure/application-insights/app-insights-proactive-performance-diagnostics",
                "IsHidden": false,
                "IsEnabledByDefault": true,
                "IsInPreview": false,
                "SupportsEmailNotifications": true
              },
              "name": "slowpageloadtime",
              "enabled": true,
              "sendEmailsToSubscriptionOwners": true,
              "customEmails": []
            }
          }
        ]
      }
    },
    // =====================
    //
    // サービスバス 関連リソース
    //
    // =====================
    {
      "type": "Microsoft.ServiceBus/namespaces",
      "apiVersion": "2022-10-01-preview",
      "name": "[parameters('serviceBus-namespace')]",
      "location": "[parameters('global-location')]",
      "sku": {
        "name": "[parameters('serviceBus-skuName')]"
      },
      "properties": {
        "premiumMessagingPartitions": 0,
        "minimumTlsVersion": "1.2",
        "publicNetworkAccess": "Enabled",
        "disableLocalAuth": false,
        "zoneRedundant": false
      }
    },
    {
      "type": "Microsoft.ServiceBus/namespaces/authorizationrules",
      "apiVersion": "2022-10-01-preview",
      "name": "[concat(parameters('serviceBus-namespace'), '/RootManageSharedAccessKey')]",
      "location": "japaneast",
      "dependsOn": [
        "[resourceId('Microsoft.ServiceBus/namespaces', parameters('serviceBus-namespace'))]"
      ],
      "properties": {
        "rights": [
          "Listen",
          "Manage",
          "Send"
        ]
      }
    },
    {
      "type": "Microsoft.ServiceBus/namespaces/networkRuleSets",
      "apiVersion": "2022-10-01-preview",
      "name": "[concat(parameters('serviceBus-namespace'), '/default')]",
      "location": "Japan East",
      "dependsOn": [
        "[resourceId('Microsoft.ServiceBus/namespaces', parameters('serviceBus-namespace'))]"
      ],
      "properties": {
        "publicNetworkAccess": "Enabled",
        "defaultAction": "Allow",
        "virtualNetworkRules": [],
        "ipRules": []
      }
    },
    // =====================
    //
    // ネットワーク 関連リソース
    //
    // =====================
    {
      "condition": "[parameters('net-vnetEnabled')]",
      "type": "Microsoft.Network/virtualNetworks",
      "apiVersion": "2020-11-01",
      "name": "[parameters('net-vnetName')]",
      "dependsOn": [
        "[resourceId('Microsoft.Network/natGateways', parameters('net-natGwName'))]"
      ],
      "location": "[parameters('global-location')]",
      "properties": {
        "addressSpace": {
          "addressPrefixes": [
            "[parameters('net-vnet-addressPrefixes')]"
          ]
        },
        "subnets": [
          {
            "type": "Microsoft.Network/virtualNetworks/subnets",
            "name": "[parameters('net-apiEndpointsubnetName')]",
            "properties": {
              "addressPrefix": "[parameters('net-subnet-addressPrefixes')]",
              "natGateway": {
                "id": "[resourceId('Microsoft.Network/natGateways', parameters('net-natGwName'))]"
              },
              "serviceEndpoints": [
                {
                  "service": "Microsoft.Storage",
                  "locations": [
                    "japaneast",
                    "japanwest"
                  ]
                }
              ],
              "delegations": [
                {
                  "type": "Microsoft.Network/virtualNetworks/subnets/delegations",
                  "name": "Microsoft.Web.serverFarms",
                  "properties": {
                    "serviceName": "Microsoft.Web/serverFarms"
                  }
                }
              ],
              "privateEndpointNetworkPolicies": "Enabled",
              "privateLinkServiceNetworkPolicies": "Enabled"
            }
          }
        ]
      }
    },
    {
      "condition": "[parameters('net-vnetEnabled')]",
      "type": "Microsoft.Network/natGateways",
      "apiVersion": "2022-05-01",
      "name": "[parameters('net-natGwName')]",
      "dependsOn": [
        "[resourceId('Microsoft.Network/publicIPAddresses', parameters('net-publicIpName'))]"
      ],
      "location": "[parameters('global-location')]",
      "sku": {
        "name": "Standard",
        "tier": "Regional"
      },
      "properties": {
        "idleTimeoutInMinutes": 4,
        "publicIpAddresses": [
          {
            "id": "[resourceId('Microsoft.Network/publicIPAddresses', parameters('net-publicIpName'))]"
          }
        ]
      }
    },
    {
      "condition": "[parameters('net-vnetEnabled')]",
      "type": "Microsoft.Network/publicIPAddresses",
      "apiVersion": "2022-05-01",
      "name": "[parameters('net-publicIpName')]",
      "location": "[parameters('global-location')]",
      "sku": {
        "name": "Standard",
        "tier": "Regional"
      },
      "properties": {
        "natGateway": {
          "id": "[resourceId('Microsoft.Network/natGateways', parameters('net-natGwName'))]"
        },
        "publicIPAddressVersion": "IPv4",
        "publicIPAllocationMethod": "Static",
        "idleTimeoutInMinutes": 4,
        "ipTags": []
      }
    }
  ],
  "outputs": {
    "LogWorkspaceName": {
      "type": "string",
      "value": "[parameters('appInsights-workspaceName')]"
    }
  }
}
