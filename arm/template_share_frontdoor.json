{
  "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
  "contentVersion": "1.0.0.0",
  "parameters": {
    "global-location": {
      "type": "string"
    },
    "global-companies": {
      "type": "array"
    },
    // =====================
    //
    // Web Application Firewall関連パラメータ
    //
    // =====================
    "waf-enabled": {
      "type": "bool"
    },
    "waf-policyName": {
      "type": "string"
    },
    "waf-frontdoorName": {
      "type": "string"
    },
    "waf-frontdoorDiagnosticName": {
      "type": "string"
    },
    "waf-routingName-default": {
      "type": "string"
    },
    // =====================
    //
    // Application Insights関連パラメータ
    //
    // =====================
    "appInsights-workspaceName": {
      "type": "string"
    }
  },
  "variables": {
    "waf-frontendEndpointName": "[concat(parameters('waf-frontdoorName'), '-azurefd-net')]",

    "waf-frontendEndpointsStatic": [
      {
        "id": "[resourceId('Microsoft.Network/frontdoors/FrontendEndpoints', parameters('waf-frontdoorName'), variables('waf-frontendEndpointName'))]",
        "name": "[variables('waf-frontendEndpointName')]",
        "properties": {
          "resourceState": "Enabled",
          "hostName": "[concat(parameters('waf-frontdoorName'), '.azurefd.net')]",
          "sessionAffinityEnabledState": "Disabled",
          "sessionAffinityTtlSeconds": 0,
          "webApplicationFirewallPolicyLink": {
            "id": "[resourceId('Microsoft.Network/frontdoorwebapplicationfirewallpolicies', parameters('waf-policyName'))]"
          }
        }
      }
    ],
    "copy": [
      {
        "name": "waf-routingRulesDynamic",
        "count": "[length(parameters('global-companies'))]",
        "input": {
          "id": "[resourceId('Microsoft.Network/frontdoors/RoutingRules', parameters('waf-frontdoorName'), parameters('global-companies')[copyIndex('waf-routingRulesDynamic')]['waf-routingName'])]",
          "name": "[parameters('global-companies')[copyIndex('waf-routingRulesDynamic')]['waf-routingName']]",
          "properties": {
            "routeConfiguration": {
              "customForwardingPath": "[parameters('global-companies')[copyIndex('waf-routingRulesDynamic')]['waf-customForwardingPath']]",
              "forwardingProtocol": "HttpsOnly",
              "backendPool": {
                "id": "[resourceId('Microsoft.Network/frontdoors/backendPools', parameters('waf-frontdoorName'), parameters('global-companies')[copyIndex('waf-routingRulesDynamic')]['waf-backendPoolName'])]"
              },
              "@odata.type": "#Microsoft.Azure.FrontDoor.Models.FrontdoorForwardingConfiguration"
            },
            "resourceState": "Enabled",
            "frontendEndpoints": [
              {
                "id": "[resourceId('Microsoft.Network/frontdoors/frontendEndpoints', parameters('waf-frontdoorName'), variables('waf-frontendEndpointName'))]"
              },
              {
                "id": "[resourceId('Microsoft.Network/frontdoors/frontendEndpoints', parameters('waf-frontdoorName'), parameters('global-companies')[copyIndex('waf-routingRulesDynamic')]['waf-frontendEndpointName'])]"
              }
            ],
            "acceptedProtocols": [
              "Https"
            ],
            "patternsToMatch": "[parameters('global-companies')[copyIndex('waf-routingRulesDynamic')]['waf-patternsToMatch']]",
            "enabledState": "Enabled"
          }
        }
      },
      {
        "name": "waf-frontendEndpointsDynamic",
        "count": "[length(parameters('global-companies'))]",
        "input": {
          "id": "[resourceId('Microsoft.Network/frontdoors/FrontendEndpoints', parameters('waf-frontdoorName'), parameters('global-companies')[copyIndex('waf-frontendEndpointsDynamic')]['waf-frontendEndpointName'])]",
          "name": "[parameters('global-companies')[copyIndex('waf-frontendEndpointsDynamic')]['waf-frontendEndpointName']]",
          "properties": {
            "resourceState": "Enabled",
            "hostName": "[parameters('global-companies')[copyIndex('waf-frontendEndpointsDynamic')]['frontDoor-domainName']]",
            "sessionAffinityEnabledState": "Disabled",
            "sessionAffinityTtlSeconds": 0
          }
        }
      },
      {
        "name": "waf-frontendEndpointIdsDynamic",
        "count": "[length(parameters('global-companies'))]",
        "input": {
          "id": "[resourceId('Microsoft.Network/frontdoors/FrontendEndpoints', parameters('waf-frontdoorName'), parameters('global-companies')[copyIndex('waf-frontendEndpointIdsDynamic')]['waf-frontendEndpointName'])]"
        }
      },
      {
        "name": "frontendEndpoints",
        "count": "[length(variables('waf-frontendEndpointIds'))]",
        "input": {
          "id": "[variables('waf-frontendEndpointIds')[copyIndex('frontendEndpoints')]['id']]"
        }
      }
    ],
    "waf-frontendEndpointIdsStatic": [
      {
        "id": "[resourceId('Microsoft.Network/frontdoors/frontendEndpoints', parameters('waf-frontdoorName'), variables('waf-frontendEndpointName'))]"
      }
    ],
    "waf-frontendEndpointIds": "[concat(variables('waf-frontendEndpointIdsStatic'), variables('waf-frontendEndpointIdsDynamic'))]",
    "waf-routingRulesStatic": [
      {
        "id": "[resourceId('Microsoft.Network/frontdoors/RoutingRules', parameters('waf-frontdoorName'), parameters('waf-routingName-default'))]",
        "name": "[parameters('waf-routingName-default')]",
        "properties": {
          "routeConfiguration": {
            "redirectType": "Found",
            "redirectProtocol": "HttpsOnly",
            "customHost": "teams.microsoft.com",
            "customPath": "/",
            "@odata.type": "#Microsoft.Azure.FrontDoor.Models.FrontdoorRedirectConfiguration"
          },
          "resourceState": "Enabled",
          "frontendEndpoints": "[variables('frontendEndpoints')]",
          "acceptedProtocols": [
            "Http",
            "Https"
          ],
          "patternsToMatch": [
            "/*"
          ],
          "enabledState": "Enabled"
        }
      }
    ],
    "waf-routingRules": "[concat(variables('waf-routingRulesStatic'), variables('waf-routingRulesDynamic'))]",
    "waf-frontendEndpoints": "[concat(variables('waf-frontendEndpointsStatic'), variables('waf-frontendEndpointsDynamic'))]"
  },
  "functions": [],
  "resources": [
    // =====================
    //
    // Web Application Firewall関連リソース
    //
    // =====================
    {
      "condition": "[parameters('waf-enabled')]",
      "type": "Microsoft.Network/frontdoorwebapplicationfirewallpolicies",
      "apiVersion": "2020-11-01",
      "name": "[parameters('waf-policyName')]",
      "location": "Global",
      "tags": {
        "app": "attane",
        "purpose": "app execution"
      },
      "sku": {
        "name": "Classic_AzureFrontDoor"
      },
      "properties": {
        "policySettings": {
          "enabledState": "Enabled",
          "mode": "Prevention",
          "customBlockResponseStatusCode": 403,
          "requestBodyCheck": "Disabled"
        },
        "customRules": {
          "rules": []
        },
        "managedRules": {
          "managedRuleSets": [
            {
              "ruleSetType": "Microsoft_DefaultRuleSet",
              "ruleSetVersion": "1.1",
              "ruleGroupOverrides": [
                {
                  "ruleGroupName": "SQLI",
                  "rules": [
                    {
                      "ruleId": "942450",
                      "enabledState": "Enabled",
                      "action": "Block",
                      "exclusions": [
                        {
                          "matchVariable": "RequestCookieNames",
                          "selectorMatchOperator": "Equals",
                          "selector": "ai_user"
                        },
                        {
                          "matchVariable": "RequestCookieNames",
                          "selectorMatchOperator": "Equals",
                          "selector": "ai_session"
                        }
                      ]
                    },
                    {
                      "ruleId": "942210",
                      "enabledState": "Enabled",
                      "action": "Block",
                      "exclusions": [
                          {
                              "matchVariable": "RequestCookieNames",
                              "selectorMatchOperator": "Equals",
                              "selector": "ai_session"
                          }
                      ]
                    },
                    {
                        "ruleId": "942330",
                        "enabledState": "Enabled",
                        "action": "Block",
                        "exclusions": [
                            {
                                "matchVariable": "RequestCookieNames",
                                "selectorMatchOperator": "Equals",
                                "selector": "ai_session"
                            }
                        ]
                    }
                  ],
                  "exclusions": []
                },
                  {
                    "ruleGroupName": "RCE",
                    "rules": [
                        {
                            "ruleId": "932150",
                            "enabledState": "Enabled",
                            "action": "Block",
                            "exclusions": [
                                {
                                    "matchVariable": "RequestCookieNames",
                                    "selectorMatchOperator": "Equals",
                                    "selector": "ai_session"
                                }
                            ]
                        }
                    ],
                    "exclusions": []
                }
              ],
              "exclusions": []
            }
          ]
        }
      }
    },
    {
      "condition": "[parameters('waf-enabled')]",
      "type": "Microsoft.Network/frontdoors",
      "apiVersion": "2021-06-01",
      "name": "[parameters('waf-frontdoorName')]",
      "location": "Global",
      "dependsOn": [
        "[resourceId('Microsoft.Network/frontdoorwebapplicationfirewallpolicies', parameters('waf-policyName'))]"
      ],
      "tags": {
        "app": "attane",
        "purpose": "app execution"
      },
      "properties": {
        "copy": [
          {
            "name": "routingRules",
            "count": "[length(variables('waf-routingRules'))]",
            "input": "[variables('waf-routingRules')[copyIndex('routingRules')]]"
          },
          {
            "name": "loadBalancingSettings",
            "count": "[length(parameters('global-companies'))]",
            "input": {
              "id": "[resourceId('Microsoft.Network/frontdoors/LoadBalancingSettings', parameters('waf-frontdoorName'), parameters('global-companies')[copyIndex('loadBalancingSettings')]['waf-loadBalancingName'])]",
              "name": "[parameters('global-companies')[copyIndex('loadBalancingSettings')]['waf-loadBalancingName']]",
              "properties": {
                "resourceState": "Enabled",
                "sampleSize": 4,
                "successfulSamplesRequired": 2,
                "additionalLatencyMilliseconds": 0
              }
            }
          },
          {
            "name": "healthProbeSettings",
            "count": "[length(parameters('global-companies'))]",
            "input": {
              "id": "[resourceId('Microsoft.Network/frontdoors/HealthProbeSettings', parameters('waf-frontdoorName'), parameters('global-companies')[copyIndex('healthProbeSettings')]['waf-healthProbeName'])]",
              "name": "[parameters('global-companies')[copyIndex('healthProbeSettings')]['waf-healthProbeName']]",
              "properties": {
                "resourceState": "Enabled",
                "path": "/",
                "protocol": "Https",
                "intervalInSeconds": "[parameters('global-companies')[copyIndex('healthProbeSettings')]['waf-frontdoorProbeInterval']]",
                "enabledState": "Enabled",
                "healthProbeMethod": "Head"
              }
            }
          },
          {
            "name": "backendPools",
            "count": "[length(parameters('global-companies'))]",
            "input": {
              "id": "[resourceId('Microsoft.Network/frontdoors/BackendPools', parameters('waf-frontdoorName'), parameters('global-companies')[copyIndex('backendPools')]['waf-backendPoolName'])]",
              "name": "[parameters('global-companies')[copyIndex('backendPools')]['waf-backendPoolName']]",
              "properties": {
                "backends": [
                  {
                    "address": "[parameters('global-companies')[copyIndex('backendPools')]['appService-hostName']]",
                    "httpPort": 80,
                    "httpsPort": 443,
                    "priority": 1,
                    "weight": 50,
                    "backendHostHeader": "[parameters('global-companies')[copyIndex('backendPools')]['appService-hostName']]",
                    "enabledState": "Enabled"
                  }
                ],
                "resourceState": "Enabled",
                "loadBalancingSettings": {
                  "id": "[resourceId('Microsoft.Network/frontdoors/loadBalancingSettings', parameters('waf-frontdoorName'), parameters('global-companies')[copyIndex('backendPools')]['waf-loadBalancingName'])]"
                },
                "healthProbeSettings": {
                  "id": "[resourceId('Microsoft.Network/frontdoors/healthProbeSettings', parameters('waf-frontdoorName'), parameters('global-companies')[copyIndex('backendPools')]['waf-healthProbeName'])]"
                }
              }
            }
          },
          {
            "name": "frontendEndpoints",
            "count": "[length(variables('waf-frontendEndpoints'))]",
            "input": "[variables('waf-frontendEndpoints')[copyIndex('frontendEndpoints')]]"
          }
        ],
        "backendPoolsSettings": {
          "enforceCertificateNameCheck": "Enabled",
          "sendRecvTimeoutSeconds": 30
        },
        "enabledState": "Enabled",
        "friendlyName": "[parameters('waf-frontdoorName')]"
      }
    },
    {
      "condition": "[parameters('waf-enabled')]",
      "apiVersion": "2017-05-01-preview",
      "name": "[concat(parameters('waf-frontdoorName'), '/Microsoft.Insights/', parameters('waf-frontdoorDiagnosticName'))]",
      "type": "Microsoft.Network/frontdoors/providers/diagnosticSettings",
      "location": "[parameters('global-location')]",
      "dependsOn": [
        "[resourceId('Microsoft.Network/frontdoors', parameters('waf-frontdoorName'))]"
      ],
      "properties": {
        "logs": [
          {
            "category": "FrontdoorAccessLog",
            "enabled": true
          },
          {
            "category": "FrontdoorWebApplicationFirewallLog",
            "enabled": true
          }
        ],
        "metrics": [
          {
            "category": "AllMetrics",
            "enabled": true
          }
        ],
        "workspaceId": "[resourceId('Microsoft.OperationalInsights/workspaces', parameters('appInsights-workspaceName'))]"
      }
    }
  ],
  "outputs": {
    "WafEnabled": {
      "type": "bool",
      "value": "[parameters('waf-enabled')]"
    },
    "FrontdoorName": {
      "condition": "[parameters('waf-enabled')]",
      "type": "string",
      "value": "[parameters('waf-frontdoorName')]"
    },
    "DiagnosticName": {
      "condition": "[parameters('waf-enabled')]",
      "type": "string",
      "value": "[parameters('waf-frontdoorDiagnosticName')]"
    }
  }
}