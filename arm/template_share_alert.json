{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"global-location": {"type": "string"}, "resourceGroup-Name": {"type": "string"}, "alert-enabled": {"type": "bool"}, "alert-actionGroupsName": {"type": "string"}, "alert-actionGroupShortName": {"type": "string"}, "alert-emailReceivers": {"type": "array"}, "net-publicIpName": {"type": "string"}, "appServicePlan-hostingPlanName": {"type": "string"}, "serviceBus-namespace": {"type": "string"}, "appInsights-componentsName": {"type": "string"}, "appServicePlan-cpuPercintageAlertsName": {"type": "string"}, "appServicePlan-memoryPercentageAlertsName": {"type": "string"}, "publicIP-ddosAlertsName": {"type": "string"}, "serviceBus-queueCountAlertsName": {"type": "string"}, "resourceHealth-Alerts": {"type": "string"}, "appInsights-unhandledExceptionAlertsName": {"type": "string"}, "resourceHealth-ActivityLogAlerts": {"type": "string"}, "appInsights-callerUnmatchAlertsName": {"type": "string"}, "appInsights-tooManyRetryAlertsName": {"type": "string"}, "appInsights-authErrorAlertsName": {"type": "string"}, "appInsights-missingEnvValuesAlertsName": {"type": "string"}, "appInsights-unautorizedAppAlertsName": {"type": "string"}, "appInsights-apiCallErrorAlertsName": {"type": "string"}, "appInsights-functionsUnrecoverableErrorAlertsName": {"type": "string"}, "appInsights-functionsRecoverableErrorAlertsName": {"type": "string"}, "logAnalyticsWorkspace-componentsName": {"type": "string"}, "logAnalyticsWorkspace-overAdditionalAmountName": {"type": "string"}, "logAnalyticsWorkspace-overAdditionalAmountName-threshold": {"type": "int"}}, "resources": [{"condition": "[parameters('alert-enabled')]", "type": "microsoft.insights/actionGroups", "apiVersion": "2023-01-01", "name": "[parameters('alert-actionGroupsName')]", "location": "Global", "properties": {"groupShortName": "[parameters('alert-actionGroupShortName')]", "enabled": true, "emailReceivers": "[parameters('alert-emailReceivers')]", "smsReceivers": [], "webhookReceivers": [], "eventHubReceivers": [], "itsmReceivers": [], "azureAppPushReceivers": [], "automationRunbookReceivers": [], "voiceReceivers": [], "logicAppReceivers": [], "azureFunctionReceivers": [], "armRoleReceivers": []}}, {"condition": "[parameters('alert-enabled')]", "type": "microsoft.insights/metricAlerts", "apiVersion": "2018-03-01", "name": "[parameters('publicIP-ddosAlertsName')]", "location": "global", "dependsOn": ["[resourceId('microsoft.insights/actionGroups', parameters('alert-actionGroupsName'))]"], "properties": {"description": "DDoS攻撃検知", "severity": 0, "enabled": true, "scopes": ["[resourceId('Microsoft.Network/publicIPAddresses', parameters('net-publicIpName'))]"], "evaluationFrequency": "PT15M", "windowSize": "PT15M", "criteria": {"allOf": [{"threshold": 0, "name": "Metric1", "metricNamespace": "Microsoft.Network/publicIPAddresses", "metricName": "IfUnderDDo<PERSON>ttack", "operator": "GreaterThan", "timeAggregation": "Maximum", "skipMetricValidation": false, "criterionType": "StaticThresholdCriterion"}], "odata.type": "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"}, "autoMitigate": false, "targetResourceType": "Microsoft.Network/publicIPAddresses", "targetResourceRegion": "[parameters('global-location')]", "actions": [{"actionGroupId": "[resourceId('Microsoft.Insights/actionGroups', parameters('alert-actionGroupsName'))]", "webHookProperties": {}}]}}, {"condition": "[parameters('alert-enabled')]", "type": "microsoft.insights/metricAlerts", "apiVersion": "2018-03-01", "name": "[parameters('appServicePlan-memoryPercentageAlertsName')]", "location": "global", "dependsOn": ["[resourceId('microsoft.insights/actionGroups', parameters('alert-actionGroupsName'))]"], "properties": {"description": "メモリ使用率", "severity": 2, "enabled": true, "scopes": ["[resourceId('Microsoft.Web/serverfarms', parameters('appServicePlan-hostingPlanName'))]"], "evaluationFrequency": "PT1M", "windowSize": "PT5M", "criteria": {"allOf": [{"threshold": 80, "name": "Metric1", "metricNamespace": "Microsoft.Web/serverFarms", "metricName": "MemoryPercentage", "operator": "GreaterThan", "timeAggregation": "Average", "skipMetricValidation": false, "criterionType": "StaticThresholdCriterion"}], "odata.type": "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"}, "autoMitigate": false, "targetResourceType": "Microsoft.Web/serverFarms", "targetResourceRegion": "[parameters('global-location')]", "actions": [{"actionGroupId": "[resourceId('Microsoft.Insights/actionGroups', parameters('alert-actionGroupsName'))]", "webHookProperties": {}}]}}, {"condition": "[parameters('alert-enabled')]", "type": "microsoft.insights/metricAlerts", "apiVersion": "2018-03-01", "name": "[parameters('serviceBus-queueCountAlertsName')]", "location": "global", "dependsOn": ["[resourceId('microsoft.insights/actionGroups', parameters('alert-actionGroupsName'))]"], "properties": {"description": "サービスバスのキューカウントチェック", "severity": 2, "enabled": true, "scopes": ["[resourceId('Microsoft.ServiceBus/namespaces', parameters('serviceBus-namespace'))]"], "evaluationFrequency": "PT5M", "windowSize": "PT15M", "criteria": {"allOf": [{"threshold": 200, "name": "Metric1", "metricNamespace": "Microsoft.ServiceBus/namespaces", "metricName": "Messages", "dimensions": [{"name": "EntityName", "operator": "Include", "values": ["*"]}], "operator": "GreaterThan", "timeAggregation": "Average", "skipMetricValidation": false, "criterionType": "StaticThresholdCriterion"}], "odata.type": "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"}, "autoMitigate": false, "targetResourceType": "Microsoft.ServiceBus/namespaces", "targetResourceRegion": "[parameters('global-location')]", "actions": [{"actionGroupId": "[resourceId('Microsoft.Insights/actionGroups', parameters('alert-actionGroupsName'))]", "webHookProperties": {}}]}}, {"condition": "[parameters('alert-enabled')]", "type": "microsoft.insights/metricAlerts", "apiVersion": "2018-03-01", "name": "[parameters('appServicePlan-cpuPercintageAlertsName')]", "location": "global", "dependsOn": ["[resourceId('microsoft.insights/actionGroups', parameters('alert-actionGroupsName'))]"], "properties": {"description": "cpu平均使用率", "severity": 2, "enabled": true, "scopes": ["[resourceId('Microsoft.Web/serverfarms', parameters('appServicePlan-hostingPlanName'))]"], "evaluationFrequency": "PT1M", "windowSize": "PT5M", "criteria": {"allOf": [{"threshold": 80, "name": "Metric1", "metricNamespace": "Microsoft.Web/serverFarms", "metricName": "CpuPercentage", "operator": "GreaterThan", "timeAggregation": "Average", "skipMetricValidation": false, "criterionType": "StaticThresholdCriterion"}], "odata.type": "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"}, "autoMitigate": false, "targetResourceType": "Microsoft.Web/serverFarms", "targetResourceRegion": "[parameters('global-location')]", "actions": [{"actionGroupId": "[resourceId('Microsoft.Insights/actionGroups', parameters('alert-actionGroupsName'))]", "webHookProperties": {}}]}}, {"condition": "[parameters('alert-enabled')]", "type": "microsoft.insights/scheduledqueryrules", "apiVersion": "2022-08-01-preview", "name": "[parameters('appInsights-unhandledExceptionAlertsName')]", "location": "[parameters('global-location')]", "dependsOn": ["[resourceId('microsoft.insights/actionGroups', parameters('alert-actionGroupsName'))]"], "properties": {"displayName": "[parameters('appInsights-unhandledExceptionAlertsName')]", "description": "キャッチされていない例外", "severity": 2, "enabled": true, "evaluationFrequency": "PT5M", "scopes": ["[resourceId('microsoft.insights/components', parameters('appInsights-componentsName'))]"], "targetResourceTypes": ["microsoft.insights/components"], "windowSize": "PT5M", "overrideQueryTimeRange": "P2D", "criteria": {"allOf": [{"query": "exceptions\n| where timestamp > ago(5min)\n| summarize count() by ['type']\n", "timeAggregation": "Maximum", "metricMeasureColumn": "count_", "dimensions": [], "operator": "GreaterThan", "threshold": 10, "failingPeriods": {"numberOfEvaluationPeriods": 1, "minFailingPeriodsToAlert": 1}}]}, "autoMitigate": false, "actions": {"actionGroups": ["[resourceId('Microsoft.Insights/actionGroups', parameters('alert-actionGroupsName'))]"], "customProperties": {}}}}, {"condition": "[parameters('alert-enabled')]", "type": "microsoft.insights/scheduledqueryrules", "apiVersion": "2022-08-01-preview", "name": "[parameters('appInsights-apiCallErrorAlertsName')]", "location": "[parameters('global-location')]", "dependsOn": ["[resourceId('microsoft.insights/actionGroups', parameters('alert-actionGroupsName'))]"], "properties": {"displayName": "[parameters('appInsights-apiCallErrorAlertsName')]", "description": "API呼び出し時エラー", "severity": 2, "enabled": true, "evaluationFrequency": "PT5M", "scopes": ["[resourceId('microsoft.insights/components', parameters('appInsights-componentsName'))]"], "targetResourceTypes": ["microsoft.insights/components"], "windowSize": "PT5M", "criteria": {"allOf": [{"query": "customEvents\n| where name has \"API_REQUEST_FAIL\" or name has \"SEARCH_SPO_LIST_REJECTS\" or name has \"SEARCH_STATE_ERROR\" or name has \"IS_OFFLINE_OR_SOMETHING_WRONG\" or name has \"QUEUE_SEND_FAIL\"\n\n", "timeAggregation": "Count", "dimensions": [], "operator": "GreaterThan", "threshold": 10, "failingPeriods": {"numberOfEvaluationPeriods": 1, "minFailingPeriodsToAlert": 1}}]}, "autoMitigate": false, "actions": {"actionGroups": ["[resourceId('Microsoft.Insights/actionGroups', parameters('alert-actionGroupsName'))]"], "customProperties": {}}}}, {"condition": "[parameters('alert-enabled')]", "type": "microsoft.insights/scheduledqueryrules", "apiVersion": "2022-08-01-preview", "name": "[parameters('appInsights-authErrorAlertsName')]", "location": "[parameters('global-location')]", "dependsOn": ["[resourceId('microsoft.insights/actionGroups', parameters('alert-actionGroupsName'))]"], "properties": {"displayName": "[parameters('appInsights-authErrorAlertsName')]", "description": "認証エラー", "severity": 3, "enabled": true, "evaluationFrequency": "PT5M", "scopes": ["[resourceId('microsoft.insights/components', parameters('appInsights-componentsName'))]"], "targetResourceTypes": ["microsoft.insights/components"], "windowSize": "PT5M", "criteria": {"allOf": [{"query": " traces\n| where message has \"Authentication failed.\"\n", "timeAggregation": "Count", "dimensions": [], "operator": "GreaterThan", "threshold": 10, "failingPeriods": {"numberOfEvaluationPeriods": 1, "minFailingPeriodsToAlert": 1}}]}, "autoMitigate": false, "actions": {"actionGroups": ["[resourceId('Microsoft.Insights/actionGroups', parameters('alert-actionGroupsName'))]"], "customProperties": {}}}}, {"condition": "[parameters('alert-enabled')]", "type": "microsoft.insights/scheduledqueryrules", "apiVersion": "2022-08-01-preview", "name": "[parameters('appInsights-missingEnvValuesAlertsName')]", "location": "[parameters('global-location')]", "dependsOn": ["[resourceId('microsoft.insights/actionGroups', parameters('alert-actionGroupsName'))]"], "properties": {"displayName": "[parameters('appInsights-missingEnvValuesAlertsName')]", "description": "環境変数異常", "severity": 0, "enabled": true, "evaluationFrequency": "PT5M", "scopes": ["[resourceId('microsoft.insights/components', parameters('appInsights-componentsName'))]"], "targetResourceTypes": ["microsoft.insights/components"], "windowSize": "PT5M", "criteria": {"allOf": [{"query": " customEvents\n| where name has \"MISSING_GLOBAL_VARIABLES\"\n  or name has \"FETCH_CONTEXT_INFO_FAIL\"\n  or name has \"MISSING_APP_INFO_MESSAGES\"\n", "timeAggregation": "Count", "dimensions": [], "operator": "GreaterThan", "threshold": 1, "failingPeriods": {"numberOfEvaluationPeriods": 1, "minFailingPeriodsToAlert": 1}}]}, "autoMitigate": false, "actions": {"actionGroups": ["[resourceId('Microsoft.Insights/actionGroups', parameters('alert-actionGroupsName'))]"], "customProperties": {}}}}, {"condition": "[parameters('alert-enabled')]", "type": "microsoft.insights/scheduledqueryrules", "apiVersion": "2022-08-01-preview", "name": "[parameters('appInsights-unautorizedAppAlertsName')]", "location": "japaneast", "properties": {"displayName": "[parameters('appInsights-unautorizedAppAlertsName')]", "description": "未許可アプリからのリクエスト", "severity": 2, "enabled": true, "evaluationFrequency": "PT10M", "scopes": ["[resourceId('microsoft.insights/components', parameters('appInsights-componentsName'))]"], "targetResourceTypes": ["microsoft.insights/components"], "windowSize": "PT10M", "criteria": {"allOf": [{"query": " traces\n| where message has \"The client application is not allowed\"\n\n", "timeAggregation": "Count", "dimensions": [], "operator": "GreaterThan", "threshold": 1, "failingPeriods": {"numberOfEvaluationPeriods": 1, "minFailingPeriodsToAlert": 1}}]}, "autoMitigate": false, "actions": {"actionGroups": ["[resourceId('Microsoft.Insights/actionGroups', parameters('alert-actionGroupsName'))]"], "customProperties": {}}}}, {"condition": "[parameters('alert-enabled')]", "type": "microsoft.insights/scheduledqueryrules", "apiVersion": "2022-08-01-preview", "name": "[parameters('appInsights-callerUnmatchAlertsName')]", "location": "[parameters('global-location')]", "dependsOn": ["[resourceId('microsoft.insights/actionGroups', parameters('alert-actionGroupsName'))]"], "properties": {"displayName": "[parameters('appInsights-callerUnmatchAlertsName')]", "description": "認証エラー（呼び出し元ID不一致）", "severity": 2, "enabled": true, "evaluationFrequency": "PT10M", "scopes": ["[resourceId('microsoft.insights/components', parameters('appInsights-componentsName'))]"], "targetResourceTypes": ["microsoft.insights/components"], "windowSize": "PT10M", "criteria": {"allOf": [{"query": " traces\n| where message has \"Auth callerId\" and message has \"が一致しません\"\n", "timeAggregation": "Total", "metricMeasureColumn": "itemCount", "dimensions": [], "operator": "GreaterThan", "threshold": 5, "failingPeriods": {"numberOfEvaluationPeriods": 1, "minFailingPeriodsToAlert": 1}}]}, "autoMitigate": false, "actions": {"actionGroups": ["[resourceId('Microsoft.Insights/actionGroups', parameters('alert-actionGroupsName'))]"], "customProperties": {}}}}, {"condition": "[parameters('alert-enabled')]", "type": "microsoft.insights/scheduledqueryrules", "apiVersion": "2022-08-01-preview", "name": "[parameters('appInsights-tooManyRetryAlertsName')]", "location": "[parameters('global-location')]", "dependsOn": ["[resourceId('microsoft.insights/actionGroups', parameters('alert-actionGroupsName'))]"], "properties": {"displayName": "[parameters('appInsights-tooManyRetryAlertsName')]", "description": "リトライ回数超過", "severity": 3, "enabled": true, "evaluationFrequency": "PT30M", "scopes": ["[resourceId('microsoft.insights/components', parameters('appInsights-componentsName'))]"], "targetResourceTypes": ["microsoft.insights/components"], "windowSize": "PT30M", "criteria": {"allOf": [{"query": " customEvents\n | where name has \"TOO_MANY_RETRY\"\n", "timeAggregation": "Count", "dimensions": [], "operator": "GreaterThan", "threshold": 10, "failingPeriods": {"numberOfEvaluationPeriods": 1, "minFailingPeriodsToAlert": 1}}]}, "autoMitigate": false, "actions": {"actionGroups": ["[resourceId('Microsoft.Insights/actionGroups', parameters('alert-actionGroupsName'))]"], "customProperties": {}}}}, {"condition": "[parameters('alert-enabled')]", "type": "microsoft.insights/scheduledqueryrules", "apiVersion": "2022-08-01-preview", "name": "[parameters('appInsights-functionsUnrecoverableErrorAlertsName')]", "location": "[parameters('global-location')]", "dependsOn": ["[resourceId('microsoft.insights/actionGroups', parameters('alert-actionGroupsName'))]"], "properties": {"displayName": "[parameters('appInsights-functionsUnrecoverableErrorAlertsName')]", "description": "Functions実行時エラー(再起不可）", "severity": 2, "enabled": true, "evaluationFrequency": "PT15M", "scopes": ["[resourceId('microsoft.insights/components', parameters('appInsights-componentsName'))]"], "targetResourceTypes": ["microsoft.insights/components"], "windowSize": "PT15M", "criteria": {"allOf": [{"query": "traces \n| where message has \"Unrecoverable error: \"\n", "timeAggregation": "Count", "dimensions": [], "operator": "GreaterThan", "threshold": 1, "failingPeriods": {"numberOfEvaluationPeriods": 1, "minFailingPeriodsToAlert": 1}}]}, "autoMitigate": false, "actions": {"actionGroups": ["[resourceId('Microsoft.Insights/actionGroups', parameters('alert-actionGroupsName'))]"], "customProperties": {}}}}, {"condition": "[parameters('alert-enabled')]", "type": "microsoft.insights/scheduledqueryrules", "apiVersion": "2022-08-01-preview", "name": "[parameters('appInsights-functionsRecoverableErrorAlertsName')]", "location": "[parameters('global-location')]", "dependsOn": ["[resourceId('microsoft.insights/actionGroups', parameters('alert-actionGroupsName'))]"], "properties": {"displayName": "[parameters('appInsights-functionsRecoverableErrorAlertsName')]", "description": "Functions実行時エラー(再起可能）", "severity": 3, "enabled": true, "evaluationFrequency": "PT15M", "scopes": ["[resourceId('microsoft.insights/components', parameters('appInsights-componentsName'))]"], "targetResourceTypes": ["microsoft.insights/components"], "windowSize": "PT15M", "criteria": {"allOf": [{"query": "traces \n| where message has \"Recoverable error:\"\n", "timeAggregation": "Count", "dimensions": [], "operator": "GreaterThan", "threshold": 20, "failingPeriods": {"numberOfEvaluationPeriods": 1, "minFailingPeriodsToAlert": 1}}]}, "autoMitigate": false, "actions": {"actionGroups": ["[resourceId('Microsoft.Insights/actionGroups', parameters('alert-actionGroupsName'))]"], "customProperties": {}}}}, {"condition": "[parameters('alert-enabled')]", "type": "microsoft.insights/scheduledqueryrules", "apiVersion": "2021-08-01", "name": "[parameters('logAnalyticsWorkspace-overAdditionalAmountName')]", "location": "[parameters('global-location')]", "properties": {"displayName": "[parameters('logAnalyticsWorkspace-overAdditionalAmountName')]", "description": "ログ追加量の急増を検知", "severity": 2, "enabled": true, "evaluationFrequency": "PT1H", "scopes": ["[resourceId('microsoft.operationalinsights/workspaces', parameters('logAnalyticsWorkspace-componentsName'))]"], "targetResourceTypes": ["microsoft.operationalinsights/workspaces"], "windowSize": "PT1H", "overrideQueryTimeRange": "P2D", "criteria": {"allOf": [{"query": "Usage\n| where TimeGenerated > startofday(ago(1h))\n| where StartTime > startofday(ago(1h))\n| summarize IngestedGB=sum(Quantity) / 1.0E3 by bin(StartTime, 1h)\n| project StartTime, IngestedGB\n| render columnchart\n", "timeAggregation": "Total", "metricMeasureColumn": "IngestedGB", "dimensions": [], "operator": "GreaterThan", "threshold": "[parameters('logAnalyticsWorkspace-overAdditionalAmountName-threshold')]", "failingPeriods": {"numberOfEvaluationPeriods": 1, "minFailingPeriodsToAlert": 1}}]}, "autoMitigate": false, "actions": {"actionGroups": ["[resourceId('Microsoft.Insights/actionGroups', parameters('alert-actionGroupsName'))]"]}}}, {"condition": "[parameters('alert-enabled')]", "type": "microsoft.insights/activityLogAlerts", "apiVersion": "2020-10-01", "name": "[parameters('resourceHealth-ActivityLogAlerts')]", "location": "global", "dependsOn": ["[resourceId('microsoft.insights/actionGroups', parameters('alert-actionGroupsName'))]"], "properties": {"scopes": ["[subscription().id]"], "condition": {"allOf": [{"field": "category", "equals": "ServiceHealth"}, {"anyOf": [{"field": "properties.incidentType", "equals": "Incident"}, {"field": "properties.incidentType", "equals": "Maintenance"}, {"field": "properties.incidentType", "equals": "Informational"}, {"field": "properties.incidentType", "equals": "ActionRequired"}]}, {"field": "properties.impactedServices[*].ServiceName", "containsAny": ["App Service", "App Service \\ Web Apps", "Application Insights", "Log Analytics", "Storage", "Functions", "Azure Frontdoor", "Service Bus", "Virtual Network NAT", "Virtual Network"]}, {"field": "properties.impactedServices[*].ImpactedRegions[*].RegionName", "containsAny": ["Global"]}]}, "actions": {"actionGroups": [{"actionGroupId": "[resourceId('Microsoft.Insights/actionGroups', parameters('alert-actionGroupsName'))]", "webhookProperties": {}}]}, "enabled": true, "description": "サービスの問題、計画メンテナンス正常性の勧告"}}, {"condition": "[parameters('alert-enabled')]", "type": "microsoft.insights/activityLogAlerts", "apiVersion": "2020-10-01", "name": "[parameters('resourceHealth-Alerts')]", "location": "Global", "dependsOn": ["[resourceId('microsoft.insights/actionGroups', parameters('alert-actionGroupsName'))]"], "properties": {"scopes": ["[subscription().id]"], "condition": {"allOf": [{"field": "category", "equals": "ResourceHealth"}, {"anyOf": [{"field": "resourceType", "equals": "Microsoft.Web/serverFarms"}, {"field": "resourceType", "equals": "Microsoft.Network/frontdoors"}, {"field": "resourceType", "equals": "Microsoft.OperationalInsights/workspaces"}, {"field": "resourceType", "equals": "Microsoft.Network/NATGateways"}, {"field": "resourceType", "equals": "Microsoft.ServiceBus/namespaces"}, {"field": "resourceType", "equals": "Microsoft.Storage/storageAccounts"}]}, {"anyOf": [{"field": "resourceGroup", "equals": "[parameters('resourceGroup-Name')]"}]}, {"anyOf": [{"field": "properties.currentHealthStatus", "equals": "Unavailable"}, {"field": "properties.currentHealthStatus", "equals": "Degraded"}]}, {"anyOf": [{"field": "properties.previousHealthStatus", "equals": "Available"}]}, {"anyOf": [{"field": "status", "equals": "Active"}, {"field": "status", "equals": "Resolved"}]}, {"anyOf": [{"field": "properties.cause", "equals": "PlatformInitiated"}]}]}, "actions": {"actionGroups": [{"actionGroupId": "[resourceId('Microsoft.Insights/actionGroups', parameters('alert-actionGroupsName'))]", "webhookProperties": {}}]}, "enabled": true, "description": "リソースの正常性チェック"}}]}