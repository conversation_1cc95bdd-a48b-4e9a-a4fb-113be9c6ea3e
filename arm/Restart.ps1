param (
	[string]
	[Parameter(mandatory=$true)]
	$WebAppName,

	[string]
	[Parameter(mandatory=$true)]
	$FunctionAppName,

	[string]
	[Parameter(mandatory=$true)]
	$ResourceGroupName
)

# Ensures you do not inherit an AzContext in your runbook
Disable-AzContextAutosave -Scope Process

# Connect to Azure with system-assigned managed identity
$AzureContext = (Connect-AzAccount -Identity).context

# set and store context
$AzureContext = Set-AzContext -SubscriptionName $AzureContext.Subscription -DefaultProfile $AzureContext

Restart-AzWebApp -ResourceGroupName $ResourceGroupName -Name $WebAppName
Restart-AzFunctionApp -ResourceGroupName $ResourceGroupName -Name $FunctionAppName -Force
