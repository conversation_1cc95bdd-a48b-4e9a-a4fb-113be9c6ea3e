{
  "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
  "contentVersion": "*******",
  "parameters": {
    "global-location": {
      "type": "string"
    },
    // =====================
    //
    // Appservice関連パラメータ
    //
    // =====================
    "application-enabled": {
      "type": "bool"
    },
    "appServicePlan-ResourceGroup": {
      "type": "string"
    },
    "appServicePlan-hostingPlanName": {
      "type": "string"
    },
    "appService-siteName": {
      "type": "string"
    },
    "appService-hostNames": {
      "type": "array"
    },
    "appService-alwaysOn": {
      "type": "bool"
    },
    "appService-env": {
      "type": "string"
    },
    "appService-healthCheckEnabled": {
      "type": "bool"
    },
    "appService-DiagnosticName": {
      "type": "string"
    },
    "appService-domainName": {
      "type": "string"
    },
    "appService-thumbprint": {
      "type": "string"
    },
    "waf-frontdoorName": {
      "type": "string"
    },
    "accessLogs-enabled": {
      "type": "bool"
    },
    // =====================
    //
    // Application Insights関連パラメータ
    //
    // =====================
    "appInsights-workspaceName": {
      "type": "string"
    },
    "appInsights-componentsName": {
      "type": "string"
    },
    // =====================
    //
    // Storage関連パラメータ
    //
    // =====================
    "storage-accountName": {
      "type": "string"
    },
    "storage-accessTier": {
      "type": "string"
    },
    // =====================
    //
    // Functions関連パラメータ
    //
    // =====================
    "functions-siteName": {
      "type": "string"
    },
    "functions-env": {
      "type": "string"
    },
    "functions-ipSecurityEnabled": {
      "type": "bool"
    },
    "functions-healthCheckEnabled": {
      "type": "bool"
    },
    // =====================
    //
    // automation関連パラメータ
    //
    // =====================
    "automation-enabled": {
      "type": "bool"
    },
    "automation-accountName": {
      "type": "string"
    },
    "automation-runbookName": {
      "type": "string"
    },
    "automation-artifactsLocation": {
      "type": "string"
    },
    "automation-artifactsPath": {
      "type": "string"
    },
    "automation-artifactsLocationSasToken": {
      "type": "string"
    },
    "automation-scheduleName": {
      "type": "string"
    },
    "automation-jobScheduleName": {
      "type": "string"
    },
    "automation-scheduleStartTime": {
      "type": "string",
      "defaultValue": "[dateTimeAdd(utcNow('u'), 'PT30M')]"
    },
    "webAppRoleNameGuid-aa": {
      "type": "string"
    },
    "functionRoleNameGuid-aa": {
      "type": "string"
    },
    "automation-ResourceGroup": {
      "type": "string"
    },
    // =====================
    //
    // ネットワーク関連パラメータ
    //
    // =====================
    "vnet-ResourceGroup": {
      "type": "string"
    },
    "net-vnetEnabled": {
      "type": "bool"
    },
    "net-vnetName": {
      "type": "string"
    },
    "net-apiEndpointsubnetName": {
      "type": "string"
    },
    "waf-enabled": {
      "type": "bool"
    },
    "waf-frontdoorDiagnosticName": {
      "type": "string"
    },
    "net-vnetIpRules": {
      "type": "array",
      "defaultValue": []
    }
  },
  "variables": {
    "global-empty": [],
    "tables": [
      "search",
      "bookmarks",
      "users"
    ],
    "appService-ipSecurityRestrictions-set": [
      {
        "ipAddress": "AzureFrontDoor.Backend",
        "action": "Allow",
        "tag": "ServiceTag",
        "priority": 100,
        "name": "onlyFrontDoor",
        "description": "フロントドアからのアクセスを許可する"
      },
      {
        "ipAddress": "127.0.0.1/32",
        "action": "Allow",
        "tag": "Default",
        "priority": 200,
        "name": "healthCheck",
        "description": "localhostからのアクセスを許可する"
      },
      {
        "ipAddress": "Any",
        "action": "Deny",
        "priority": **********,
        "name": "Deny all",
        "description": "Deny all access"
      }
    ],
    "functions-ipSecurityRestrictions-set": [
      {
        "ipAddress": "AzureDevOps",
        "action": "Allow",
        "tag": "ServiceTag",
        "priority": 100,
        "name": "onlyAzureDevOps",
        "description": "AzureDevOpsからのみアクセスを許可する"
      },
      {
        "ipAddress": "127.0.0.1/32",
        "action": "Allow",
        "tag": "Default",
        "priority": 200,
        "name": "healthCheck",
        "description": "localhostからのアクセスを許可する"
      },
      {
        "ipAddress": "Any",
        "action": "Deny",
        "priority": **********,
        "name": "Deny all",
        "description": "Deny all access"
      }
    ],
    "storage-account-name-tidy": "[toLower(trim(parameters('storage-accountName')))]",
    "net-vnetAcls": {
      "bypass": "Metrics, AzureServices",
      "virtualNetworkRules": [
        {
          "id": "[resourceId(parameters('vnet-ResourceGroup'), 'Microsoft.Network/virtualNetworks/subnets', parameters('net-vnetName'), parameters('net-apiEndpointsubnetName'))]",
          "action": "Allow",
          "state": "Succeeded"
        }
      ],
      "ipRules": "[parameters('net-vnetIpRules')]",
      "defaultAction": "Deny"
    }
  },
  "functions": [
    {
      "namespace": "attane",
      "members": {
        "keyValue": {
          "parameters": [
            {
              "name": "key",
              "type": "string"
            },
            {
              "name": "value",
              "type": "string"
            }
          ],
          "output": {
            "type": "object",
            // 関数内に関数を定義できないので、バックスラッシュおよびダブルクォートのエスケープは直接記載する
            "value": "[json(concat('{\"', parameters('key'), '\":\"', replace(replace(parameters('value'), '\\', '\\\\'), '\"', '\\\"'), '\"}'))]"
          }
        },
        "storageConnectionString": {
          "parameters": [
            {
              "name": "accountName",
              "type": "string"
            },
            {
              "name": "accountKey",
              "type": "string"
            },
            {
              "name": "endpointSuffix",
              "type": "string"
            }
          ],
          "output": {
            "type": "string",
            "value": "[concat('DefaultEndpointsProtocol=https;AccountName=', parameters('accountName'), ';AccountKey=', parameters('accountKey'), ';EndpointSuffix=', parameters('endpointSuffix'))]"
          }
        },
        "getSuffix": {
          "parameters": [
            {
              "name": "prefix",
              "type": "string"
            },
            {
              "name": "target",
              "type": "string"
            }
          ],
          "output": {
            "type": "string",
            "value": "[replace(skip(parameters('target'), add(add(indexOf(parameters('target'), parameters('prefix')), length(parameters('prefix'))), 1)), '/', '')]"
          }
        }
      }
    }
  ],
  "resources": [
    // =====================
    //
    // Appservice関連リソース
    //
    // =====================
    {
      "condition": "[parameters('application-enabled')]",
      "apiVersion": "2021-02-01",
      "name": "[parameters('appService-siteName')]",
      "type": "Microsoft.Web/sites",
      "location": "[parameters('global-location')]",
      "identity": {
        "type": "SystemAssigned"
      },
      "tags": {},
      "properties": {
        "name": "[parameters('appService-siteName')]",
        "hostNames": "[parameters('appService-hostNames')]",
        "siteConfig": {
          "metadata": [
            {
              "name": "CURRENT_STACK",
              "value": "dotnet"
            }
          ],
          "phpVersion": "OFF",
          "netFrameworkVersion": "v8.0",
          "alwaysOn": "[parameters('appService-alwaysOn')]",
          "ftpsState": "Disabled",
          "virtualApplications": [
            {
              "virtualPath": "/",
              "physicalPath": "site\\wwwroot"
            },
            {
              "virtualPath": "/api",
              "physicalPath": "site\\wwwroot\\api"
            }
          ]
        },
        "serverFarmId": "[resourceId(parameters('appServicePlan-ResourceGroup'), 'Microsoft.Web/serverfarms', parameters('appServicePlan-hostingPlanName'))]",
        "clientAffinityEnabled": true,
        "httpsOnly": true,
        "clientCertEnabled": false,
        "clientCertMode": "Required",
        "customDomainVerificationId": "54F4AFC3EBA9BC8A5615BE4C9B04B230CADEAC7E6197BFC12CB55AEFEC451461",
        "virtualNetworkSubnetId": "[if(parameters('net-vnetEnabled'), resourceId(parameters('vnet-ResourceGroup'), 'Microsoft.Network/virtualNetworks/subnets', parameters('net-vnetName'), parameters('net-apiEndpointsubnetName')), json('null'))]",
        "keyVaultReferenceIdentity": "SystemAssigned",

        "hostNameSslStates": [
          { 
              "name": "[concat(parameters('appService-siteName'), '.azurewebsites.net')]",
              "sslState": "Disabled",
              "hostType": "Standard"
          },
          {
              "name": "[concat(parameters('appService-siteName'), '.scm.azurewebsites.net')]",
              "sslState": "Disabled",
              "hostType": "Repository"
          },
          {
            "name": "[parameters('appService-domainName')]", //AppServiceのカスタムドメイン
            "sslState": "SniEnabled", 
            "thumbprint": "[parameters('appService-thumbprint')]", //証明書の内部ID
            "hostType": "Standard" 
          }
        ]
      },
      "resources": [
        {
          "condition": "[parameters('application-enabled')]",
          "apiVersion": "2021-02-01",
          "name": "appsettings",
          "type": "config",
          "location": "[parameters('global-location')]",
          "dependsOn": [
            "[resourceId('Microsoft.Web/sites', parameters('appService-siteName'))]"
          ],
          "properties": "[union(
            attane.keyValue('ApplicationInsights:ConnectionString', reference(resourceId(parameters('appServicePlan-ResourceGroup'), 'Microsoft.Insights/components', parameters('appInsights-componentsName')), '2020-02-02').ConnectionString),
            attane.keyValue('StorageAccount:ConnectionString', concat('DefaultEndpointsProtocol=https;AccountName=', variables('storage-account-name-tidy'), ';AccountKey=', listKeys(resourceId('Microsoft.Storage/storageAccounts', variables('storage-account-name-tidy')), '2021-06-01').keys[0].value, ';EndpointSuffix=', environment().suffixes.storage)),
            json(parameters('appService-env')))]"
        },
        {
          "condition": "[parameters('application-enabled')]",
          "apiVersion": "2021-03-01",
          "name": "web",
          "type": "config",
          "location": "[parameters('global-location')]",
          "dependsOn": [
            "[resourceId('Microsoft.Web/sites', parameters('appService-siteName'))]"
          ],
          "properties": {
            "ipSecurityRestrictions": "[if(parameters('waf-enabled'), variables('appService-ipSecurityRestrictions-set'), variables('global-empty'))]",
            "healthCheckPath": "[if(parameters('appService-healthCheckEnabled'), 'api/health', '')]"
          }
        },

        {
          "type": "Microsoft.Web/sites/hostNameBindings",
          "apiVersion": "2023-01-01",
          "name": "[concat(parameters('appService-siteName'), '/', parameters('appService-domainName') )]",
          "location": "[parameters('global-location')]",
          "dependsOn": [
              "[resourceId('Microsoft.Web/sites', parameters('appService-siteName'))]"
          ],
          "properties": {
              "siteName": "[parameters('appService-siteName')]",
              "azureResourceName": "[parameters('appService-siteName')]",
              "azureResourceType": "Website",
              "customHostNameDnsRecordType": "CName",
              "hostNameType": "Managed",
              "sslState": "SniEnabled",
              "thumbprint": "[parameters('appService-thumbprint')]" //証明書の内部ID
          }
        }

      ]
    },
    {
      "condition": "[parameters('accessLogs-enabled')]",
      "apiVersion": "2017-05-01-preview",
      "name": "[concat(parameters('appService-siteName'), '/Microsoft.Insights/', parameters('appService-DiagnosticName'))]",
      "type": "Microsoft.Web/sites/providers/diagnosticSettings",
      "location": "[parameters('global-location')]",
      "dependsOn": [
        "[resourceId('Microsoft.Web/sites', parameters('appService-siteName'))]"
      ],
      "properties": {
        "logs": [
          {
            "category": "AppServiceHTTPLogs",
            "enabled": true
          }
        ],
        "workspaceId": "[resourceId(parameters('appServicePlan-ResourceGroup'), 'Microsoft.OperationalInsights/workspaces', parameters('appInsights-workspaceName'))]"
      }
    },
    // =====================
    //
    // Storage 関連リソース
    //
    // =====================
    {
      "type": "Microsoft.Storage/storageAccounts",
      "apiVersion": "2021-04-01",
      "name": "[parameters('storage-accountName')]",
      "location": "[parameters('global-location')]",
      "tags": {
        "app": "attane",
        "purpose": "app execution"
      },
      "sku": {
        "name": "Standard_LRS",
        "tier": "Standard"
      },
      "kind": "StorageV2",
      "properties": {
        "allowCrossTenantReplication": false,
        "minimumTlsVersion": "TLS1_2",
        "allowBlobPublicAccess": false,
        "allowSharedKeyAccess": true,
        "networkAcls": "[if(parameters('net-vnetEnabled'), variables('net-vnetAcls'), json('null'))]",
        "supportsHttpsTrafficOnly": true,
        "encryption": {
          "services": {
            "file": {
              "keyType": "Account",
              "enabled": true
            },
            "blob": {
              "keyType": "Account",
              "enabled": true
            }
          },
          "keySource": "Microsoft.Storage"
        },
        "accessTier": "[parameters('storage-accessTier')]"
      }
    },
    {
      "type": "Microsoft.Storage/storageAccounts/blobServices",
      "apiVersion": "2021-04-01",
      "name": "[concat(parameters('storage-accountName'), '/default')]",
      "dependsOn": [
        "[resourceId('Microsoft.Storage/storageAccounts', parameters('storage-accountName'))]"
      ],
      "sku": {
        "name": "Standard_LRS",
        "tier": "Standard"
      },
      "properties": {
        "changeFeed": {
          "enabled": false
        },
        "restorePolicy": {
          "enabled": false
        },
        "containerDeleteRetentionPolicy": {
          "enabled": false
        },
        "cors": {
          "corsRules": []
        },
        "deleteRetentionPolicy": {
          "enabled": false
        },
        "isVersioningEnabled": false
      }
    },
    {
      "type": "Microsoft.Storage/storageAccounts/fileServices",
      "apiVersion": "2021-04-01",
      "name": "[concat(parameters('storage-accountName'), '/default')]",
      "dependsOn": [
        "[resourceId('Microsoft.Storage/storageAccounts', parameters('storage-accountName'))]"
      ],
      "sku": {
        "name": "Standard_LRS",
        "tier": "Standard"
      },
      "properties": {
        "cors": {
          "corsRules": []
        }
      }
    },
    {
      "type": "Microsoft.Storage/storageAccounts/queueServices",
      "apiVersion": "2021-04-01",
      "name": "[concat(parameters('storage-accountName'), '/default')]",
      "dependsOn": [
        "[resourceId('Microsoft.Storage/storageAccounts', parameters('storage-accountName'))]"
      ],
      "properties": {
        "cors": {
          "corsRules": []
        }
      }
    },
    {
      "type": "Microsoft.Storage/storageAccounts/tableServices",
      "apiVersion": "2021-04-01",
      "name": "[concat(parameters('storage-accountName'), '/default')]",
      "dependsOn": [
        "[resourceId('Microsoft.Storage/storageAccounts', parameters('storage-accountName'))]"
      ],
      "properties": {
        "cors": {
          "corsRules": []
        }
      }
    },
    {
      "type": "Microsoft.Storage/storageAccounts/blobServices/containers",
      "apiVersion": "2021-04-01",
      "name": "[concat(parameters('storage-accountName'), '/default/azure-webjobs-hosts')]",
      "dependsOn": [
        "[resourceId('Microsoft.Storage/storageAccounts/blobServices', parameters('storage-accountName'), 'default')]",
        "[resourceId('Microsoft.Storage/storageAccounts', parameters('storage-accountName'))]"
      ],
      "properties": {
        "defaultEncryptionScope": "$account-encryption-key",
        "denyEncryptionScopeOverride": false,
        "publicAccess": "None"
      }
    },
    {
      "type": "Microsoft.Storage/storageAccounts/blobServices/containers",
      "apiVersion": "2021-04-01",
      "name": "[concat(parameters('storage-accountName'), '/default/azure-webjobs-secrets')]",
      "dependsOn": [
        "[resourceId('Microsoft.Storage/storageAccounts/blobServices', parameters('storage-accountName'), 'default')]",
        "[resourceId('Microsoft.Storage/storageAccounts', parameters('storage-accountName'))]"
      ],
      "properties": {
        "defaultEncryptionScope": "$account-encryption-key",
        "denyEncryptionScopeOverride": false,
        "publicAccess": "None"
      }
    },
    {
      "copy": {
        "name": "table",
        "count": "[length(variables('tables'))]"
      },
      "type": "Microsoft.Storage/storageAccounts/tableServices/tables",
      "apiVersion": "2021-06-01",
      "name": "[concat(parameters('storage-accountName'), '/default/', variables('tables')[copyIndex('table')])]",
      "dependsOn": [
        "[resourceId('Microsoft.Storage/storageAccounts/tableServices', parameters('storage-accountName'), 'default')]",
        "[resourceId('Microsoft.Storage/storageAccounts', parameters('storage-accountName'))]"
      ]
    },
    {
      "type": "Microsoft.Storage/storageAccounts/tableServices/providers/diagnosticSettings",
      "apiVersion": "2021-05-01-preview",
      "name": "[concat(parameters('storage-accountName'),'/default/Microsoft.Insights/','Send to all locations')]",
      "dependsOn": [
        "[resourceId('Microsoft.Storage/storageAccounts', parameters('storage-accountName'))]"
      ],
      "properties": {
        "workspaceId": "[resourceId(parameters('appServicePlan-ResourceGroup'), 'Microsoft.OperationalInsights/workspaces', parameters('appInsights-workspaceName'))]",
        "storageAccountId": "[resourceId('Microsoft.Storage/storageAccounts', parameters('storage-accountName'))]",
        "logs": [
          {
            "category": "StorageRead",
            "enabled": true
          },
          {
            "category": "StorageWrite",
            "enabled": true
          },
          {
            "category": "StorageDelete",
            "enabled": true
          }
        ],
        "metrics": [
          {
            "category": "Transaction",
            "enabled": true
          }
        ]
      }
    },
    // =====================
    //
    // Functions 関連リソース
    //
    // =====================
    {
      "condition": "[parameters('application-enabled')]",
      "type": "Microsoft.Web/sites",
      "apiVersion": "2021-01-15",
      "name": "[parameters('functions-siteName')]",
      "location": "[parameters('global-location')]",
      "identity": {
        "type": "SystemAssigned"
      },
      "tags": {
        "app": "attane",
        "purpose": "app execution"
      },
      "kind": "functionapp",
      "dependsOn": [
        "[resourceId('Microsoft.Storage/storageAccounts', parameters('storage-accountName'))]"
      ],
      "properties": {
        "enabled": true,
        "serverFarmId": "[resourceId(parameters('appServicePlan-ResourceGroup'), 'Microsoft.Web/serverfarms', parameters('appServicePlan-hostingPlanName'))]",
        "reserved": false,
        "siteConfig": {
          "numberOfWorkers": 1,
          "acrUseManagedIdentityCreds": false,
          "alwaysOn": true,
          "http20Enabled": false,
          "ftpsState": "Disabled",
          "functionAppScaleLimit": 0,
          "minimumElasticInstanceCount": 0
        },
        "scmSiteAlsoStopped": false,
        "clientAffinityEnabled": false,
        "clientCertEnabled": false,
        "clientCertMode": "Required",
        "hostNamesDisabled": false,
        "containerSize": 1536,
        "dailyMemoryTimeQuota": 0,
        "keyVaultReferenceIdentity": "SystemAssigned",
        "httpsOnly": true,
        "redundancyMode": "None",
        "storageAccountRequired": false,
        "virtualNetworkSubnetId": "[if(parameters('net-vnetEnabled'), resourceId(parameters('vnet-ResourceGroup'), 'Microsoft.Network/virtualNetworks/subnets', parameters('net-vnetName'), parameters('net-apiEndpointsubnetName')), json('null'))]",
        "basicPublishingCredentialsPolicies": [
          {
            "name": "ftp",
            "properties": {
              "allow": true
            }
          },
          {
            "name": "scm",
            "properties": {
              "allow": true
            }
          }
        ]
      },
      "resources": [
        {
          "condition": "[parameters('application-enabled')]",
          "apiVersion": "2021-03-01",
          "name": "web",
          "type": "config",
          "location": "[parameters('global-location')]",
          "dependsOn": [
            "[resourceId('Microsoft.Web/sites', parameters('functions-siteName'))]"
          ],
          "properties": {
            "numberOfWorkers": 1,
            "defaultDocuments": [
              "Default.htm",
              "Default.html",
              "Default.asp",
              "index.htm",
              "index.html",
              "iisstart.htm",
              "default.aspx",
              "index.php"
            ],
            "phpVersion": "5.6",
            "requestTracingEnabled": false,
            "remoteDebuggingEnabled": false,
            "httpLoggingEnabled": false,
            "acrUseManagedIdentityCreds": false,
            "logsDirectorySizeLimit": 35,
            "detailedErrorLoggingEnabled": false,
            "publishingUsername": "[concat('$', parameters('functions-siteName'))]",
            "azureStorageAccounts": {},
            "scmType": "None",
            "use32BitWorkerProcess": true,
            "webSocketsEnabled": false,
            "alwaysOn": true,
            "managedPipelineMode": "Integrated",
            "virtualApplications": [
              {
                "virtualPath": "/",
                "physicalPath": "site\\wwwroot",
                "preloadEnabled": true
              }
            ],
            "loadBalancing": "LeastRequests",
            "experiments": {
              "rampUpRules": []
            },
            "autoHealEnabled": false,
            "vnetRouteAllEnabled": true,
            "vnetPrivatePortsCount": 0,
            "ipSecurityRestrictions": "[if(parameters('functions-ipSecurityEnabled'), variables('functions-ipSecurityRestrictions-set'), variables('global-empty'))]",
            "scmIpSecurityRestrictions": [
              {
                "ipAddress": "Any",
                "action": "Allow",
                "priority": 1,
                "name": "Allow all",
                "description": "Allow all access"
              }
            ],
            "scmIpSecurityRestrictionsUseMain": false,
            "http20Enabled": false,
            "minTlsVersion": "1.2",
            "scmMinTlsVersion": "1.2",
            "preWarmedInstanceCount": 0,
            "functionAppScaleLimit": 0,
            "healthCheckPath": "[if(parameters('functions-healthCheckEnabled'), 'api/health', '')]",
            "functionsRuntimeScaleMonitoringEnabled": false,
            "minimumElasticInstanceCount": 0
          }
        },
        {
          "condition": "[parameters('application-enabled')]",
          "apiVersion": "2021-03-01",
          "name": "[concat(parameters('functions-siteName'), '.azurewebsites.net')]",
          "type": "hostNameBindings",
          "location": "[parameters('global-location')]",
          "dependsOn": [
            "[resourceId('Microsoft.Web/sites', parameters('functions-siteName'))]",
            "[resourceId('Microsoft.Storage/storageAccounts', parameters('storage-accountName'))]"
          ],
          "properties": {
            "siteName": "[parameters('functions-siteName')]",
            "hostNameType": "Verified"
          }
        }
      ]
    },
    {
      "condition": "[parameters('application-enabled')]",
      "type": "Microsoft.Web/sites/config",
      "name": "[concat(parameters('functions-siteName'), '/appsettings')]",
      "apiVersion": "2021-02-01",
      "dependsOn": [
        "[resourceId('Microsoft.Web/sites', parameters('functions-siteName'))]",
        "[resourceId('Microsoft.Storage/storageAccounts', parameters('storage-accountName'))]"
      ],
      "properties": "[union(
        attane.keyValue('APPLICATIONINSIGHTS_CONNECTION_STRING', reference(resourceId(parameters('appServicePlan-ResourceGroup'), 'Microsoft.Insights/components', parameters('appInsights-componentsName')), '2020-02-02').ConnectionString),
        attane.keyValue('AzureWebJobsStorage',
          attane.storageConnectionString(
            parameters('storage-accountName'),
            listkeys(resourceId('Microsoft.Storage/storageAccounts', parameters('storage-accountName')), '2021-04-01').keys[0].value,
            attane.getSuffix('dfs', reference(resourceId('Microsoft.Storage/storageAccounts', parameters('storage-accountName')), '2021-04-01').primaryEndPoints.dfs)
          )
        ),
        attane.keyValue('AzureWebJobsStorageConnectionString',
          attane.storageConnectionString(
            parameters('storage-accountName'),
            listkeys(resourceId('Microsoft.Storage/storageAccounts', parameters('storage-accountName')), '2021-04-01').keys[0].value,
            attane.getSuffix('dfs', reference(resourceId('Microsoft.Storage/storageAccounts', parameters('storage-accountName')), '2021-04-01').primaryEndPoints.dfs)
          )
        ),
        json(parameters('functions-env'))
      )]"
    },
    {
      "condition": "[parameters('net-vnetEnabled')]",
      "type": "Microsoft.Web/sites/networkConfig",
      "apiVersion": "2021-02-01",
      "name": "[concat(parameters('functions-siteName'), '/virtualNetwork')]",
      "location": "[parameters('global-location')]",
      "dependsOn": [
        "[resourceId('Microsoft.Web/sites', parameters('functions-siteName'))]",
        "[resourceId('Microsoft.Storage/storageAccounts', parameters('storage-accountName'))]"
      ],
      "properties": {
        "subnetResourceId": "[resourceId(parameters('vnet-ResourceGroup'), 'Microsoft.Network/virtualNetworks/subnets', parameters('net-vnetName'), parameters('net-apiEndpointsubnetName'))]",
        "swiftSupported": true
      }
    },
    // =====================
    //
    // Automation 関連リソース
    //
    // =====================
    {
      "condition": "[parameters('automation-Enabled')]",
      "type": "Microsoft.Automation/automationAccounts",
      "apiVersion": "2021-06-22",
      "name": "[parameters('automation-accountName')]",
      "location": "[parameters('global-location')]",
      "identity": {
        "type": "SystemAssigned"
      },
      "properties": {
        "publicNetworkAccess": true,
        "disableLocalAuth": false,
        "sku": {
          "name": "Basic"
        },
        "encryption": {
          "keySource": "Microsoft.Automation",
          "identity": {}
        }
      },
      "resources": [
        {
          "condition": "[parameters('automation-Enabled')]",
          "type": "runbooks",
          "apiVersion": "2019-06-01",
          "name": "[parameters('automation-runbookName')]",
          "location": "[parameters('global-location')]",
          "dependsOn": [
            "[resourceId('Microsoft.Automation/automationAccounts', parameters('automation-accountName'))]"
          ],
          "properties": {
            "runbookType": "PowerShell",
            "logVerbose": false,
            "logProgress": false,
            "logActivityTrace": 0,
            "publishContentLink": {
              "uri": "[uri(parameters('automation-artifactsLocation'), concat(parameters('automation-artifactsPath'), parameters('automation-artifactsLocationSasToken')))]",
              "version": "*******"
            }
          }
        },
        {
          "condition": "[parameters('automation-Enabled')]",
          "type": "schedules",
          "apiVersion": "2020-01-13-preview",
          "name": "[parameters('automation-scheduleName')]",
          "dependsOn": [
            "[resourceId('Microsoft.Automation/automationAccounts', parameters('automation-accountName'))]"
          ],
          "properties": {
            "description": "週2回の月曜日、木曜日の2時に定期実行する",
            "startTime": "[parameters('automation-scheduleStartTime')]",
            "expiryTime": "9999-12-31T23:59:59.9999999+00:00",
            "interval": 1,
            "frequency": "Week",
            "timeZone": "Asia/Tokyo",
            "advancedSchedule": {
              "weekDays": [
                "Monday",
                "Thursday"
              ]
            }
          }
        },
        {
          "condition": "[parameters('automation-Enabled')]",
          "type": "jobSchedules",
          "apiVersion": "2020-01-13-preview",
          "name": "[parameters('automation-jobScheduleName')]",
          "dependsOn": [
            "[resourceId('Microsoft.Automation/automationAccounts', parameters('automation-accountName'))]",
            "[resourceId('Microsoft.Automation/automationAccounts/runbooks', parameters('automation-accountName'), parameters('automation-runbookName'))]",
            "[resourceId('Microsoft.Automation/automationAccounts/schedules', parameters('automation-accountName'), parameters('automation-scheduleName'))]"
          ],
          "properties": {
            "parameters": {
              "WEBAPPNAME": "[parameters('appService-siteName')]",
              "RESOURCEGROUPNAME": "[parameters('automation-ResourceGroup')]",
              "FUNCTIONAPPNAME": "[parameters('functions-siteName')]"
            },
            "runbook": {
              "name": "[parameters('automation-runbookName')]"
            },
            "schedule": {
              "name": "[parameters('automation-scheduleName')]"
            }
          }
        }
      ]
    },
    {
      "condition": "[parameters('automation-Enabled')]",
      "type": "Microsoft.Authorization/roleAssignments",
      "apiVersion": "2020-10-01-preview",
      "name": "[parameters('webAppRoleNameGuid-aa')]",
      "scope": "[concat('Microsoft.Web/sites', '/', parameters('appService-siteName'))]",
      "dependsOn": [
        "[resourceId('Microsoft.Automation/automationAccounts', parameters('automation-accountName'))]",
        "[resourceId('Microsoft.Web/sites', parameters('appService-siteName'))]"
      ],
      "properties": {
        "roleDefinitionId": "[concat('/subscriptions/', subscription().subscriptionId, '/providers/Microsoft.Authorization/roleDefinitions/', 'b24988ac-6180-42a0-ab88-20f7382dd24c')]",
        "principalId": "[if(not(parameters('automation-Enabled')), json('null'), reference(resourceId('Microsoft.Automation/automationAccounts', parameters('automation-accountName')),'2020-01-13-preview','Full').identity.principalId)]"
      }
    },
    {
      "condition": "[parameters('automation-Enabled')]",
      "type": "Microsoft.Authorization/roleAssignments",
      "apiVersion": "2020-10-01-preview",
      "name": "[parameters('functionRoleNameGuid-aa')]",
      "scope": "[concat('Microsoft.Web/sites', '/', parameters('functions-siteName'))]",
      "dependsOn": [
        "[resourceId('Microsoft.Automation/automationAccounts', parameters('automation-accountName'))]",
        "[resourceId('Microsoft.Web/sites', parameters('functions-siteName'))]"
      ],
      "properties": {
        "roleDefinitionId": "[concat('/subscriptions/', subscription().subscriptionId, '/providers/Microsoft.Authorization/roleDefinitions/', 'b24988ac-6180-42a0-ab88-20f7382dd24c')]",
        "principalId": "[if(not(parameters('automation-Enabled')), json('null'), reference(resourceId('Microsoft.Automation/automationAccounts', parameters('automation-accountName')),'2020-01-13-preview','Full').identity.principalId)]"
      }
    }
  ],
  "outputs": {
    "WafEnabled": {
      "type": "bool",
      "value": "[parameters('waf-enabled')]"
    },
    "FrontdoorName": {
      "condition": "[parameters('waf-enabled')]",
      "type": "string",
      "value": "[parameters('waf-frontdoorName')]"
    },
    "LogWorkspaceName": {
      "type": "string",
      "value": "[parameters('appInsights-workspaceName')]"
    },
    "DiagnosticName": {
      "condition": "[parameters('waf-enabled')]",
      "type": "string",
      "value": "[parameters('waf-frontdoorDiagnosticName')]"
    }
  }
}