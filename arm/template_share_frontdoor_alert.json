{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "1.0.0.0", "parameters": {"global-location": {"type": "string"}, "alert-enabled": {"type": "bool"}, "waf-enabled": {"type": "bool"}, "alert-actionGroupsName": {"type": "string"}, "waf-frontdoorName": {"type": "string"}, "waf-fraudAlertsName": {"type": "string"}, "frontdoor-TotalLatencyAlert": {"type": "string"}, "frontdoor-TotalLatencyAlert-threshold": {"type": "int"}}, "resources": [{"condition": "[parameters('waf-enabled')]", "type": "microsoft.insights/scheduledqueryrules", "apiVersion": "2022-08-01-preview", "name": "[parameters('waf-fraudAlertsName')]", "location": "[parameters('global-location')]", "properties": {"displayName": "[parameters('waf-fraudAlertsName')]", "description": "WAFでブロックされたリクエストを検知", "severity": 2, "enabled": true, "evaluationFrequency": "PT15M", "scopes": ["[resourceId('Microsoft.Network/frontdoors', parameters('waf-frontdoorName'))]"], "targetResourceTypes": ["Microsoft.Network/frontdoors"], "windowSize": "PT15M", "criteria": {"allOf": [{"query": "AzureDiagnostics\n| where ResourceType == \"FRONTDOORS\" and Category == \"FrontdoorWebApplicationFirewallLog\"\n| where column_ifexists(\"action_s\", \"\") == \"Block\"\n| order by TimeGenerated\n", "timeAggregation": "Count", "dimensions": [], "operator": "GreaterThanOrEqual", "threshold": 1, "failingPeriods": {"numberOfEvaluationPeriods": 1, "minFailingPeriodsToAlert": 1}}]}, "autoMitigate": false, "actions": {"actionGroups": ["[resourceId('Microsoft.Insights/actionGroups', parameters('alert-actionGroupsName'))]"], "customProperties": {}}}}, {"condition": "[parameters('alert-enabled')]", "type": "microsoft.insights/metricAlerts", "apiVersion": "2018-03-01", "name": "[parameters('frontdoor-TotalLatencyAlert')]", "location": "global", "properties": {"description": "クライアント-Front Door間合計待機時間", "severity": 3, "enabled": true, "scopes": ["[resourceId('Microsoft.Network/frontdoors', parameters('waf-frontdoorName'))]"], "evaluationFrequency": "PT5M", "windowSize": "PT5M", "criteria": {"allOf": [{"threshold": "[parameters('frontdoor-TotalLatencyAlert-threshold')]", "name": "Metric1", "metricNamespace": "Microsoft.Network/frontdoors", "metricName": "TotalLatency", "operator": "GreaterThan", "timeAggregation": "Average", "criterionType": "StaticThresholdCriterion"}], "odata.type": "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"}, "autoMitigate": false, "targetResourceType": "Microsoft.Network/frontdoors", "targetResourceRegion": "global", "actions": [{"actionGroupId": "[resourceId('Microsoft.Insights/actionGroups', parameters('alert-actionGroupsName'))]"}]}}]}