{
  "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
  "contentVersion": "1.0.0.0",
  "parameters": {
    "global-location": {
      "type": "string"
    },
    // =====================
    //
    // ストレージアカウント関連パラメータ
    //
    // =====================
    "storage-accountName": {
      "type": "string"
    }
  },
  "variables": {
  },
  "functions": [],
  "resources": [
    // =====================
    //
    // ストレージアカウント関連リソース
    //
    // =====================
    {
      "type": "Microsoft.Storage/storageAccounts",
      "apiVersion": "2021-09-01",
      "name": "[parameters('storage-accountName')]",
      "location": "[parameters('global-location')]",
      "sku": {
        "name": "Standard_LRS",
        "tier": "Standard"
      },
      "kind": "StorageV2",
      "properties": {
        "dnsEndpointType": "Standard",
        "defaultToOAuthAuthentication": false,
        "publicNetworkAccess": "Enabled",
        "allowCrossTenantReplication": true,
        "minimumTlsVersion": "TLS1_2",
        "allowBlobPublicAccess": true,
        "allowSharedKeyAccess": true,
        "networkAcls": {
          "bypass": "AzureServices",
          "virtualNetworkRules": [],
          "ipRules": [],
          "defaultAction": "Allow"
        },
        "supportsHttpsTrafficOnly": true,
        "encryption": {

          "services": {
            "file": {
              "keyType": "Account",
              "enabled": true
            },
            "table": {
              // "keyType": "Account",
              "enabled": true
            },
            "queue": {
              // "keyType": "Account",
              "enabled": true
            },
            "blob": {
              "keyType": "Account",
              "enabled": true
            }
          },
          "keySource": "Microsoft.Storage"
        },
        "accessTier": "Cool"
      }
    },
    {
      "type": "Microsoft.Storage/storageAccounts/blobServices",
      "apiVersion": "2021-04-01",
      "name": "[concat(parameters('storage-accountName'), '/default')]",
      "dependsOn": [
        "[resourceId('Microsoft.Storage/storageAccounts', parameters('storage-accountName'))]"
      ],
      "sku": {
        "name": "Standard_LRS",
        "tier": "Standard"
      },
      "properties": {
        "changeFeed": {
          "enabled": false
        },
        "restorePolicy": {
          "enabled": false
        },
        "containerDeleteRetentionPolicy": {
          "enabled": false
        },
        "cors": {
          "corsRules": []
        },
        "deleteRetentionPolicy": {
          "enabled": false
        },
        "isVersioningEnabled": false
      }
    },
    {
      "type": "Microsoft.Storage/storageAccounts/blobServices/containers",
      "apiVersion": "2021-04-01",
      "name": "[concat(parameters('storage-accountName'), '/default/sources')]",
      "dependsOn": [
        "[resourceId('Microsoft.Storage/storageAccounts/blobServices', parameters('storage-accountName'), 'default')]",
        "[resourceId('Microsoft.Storage/storageAccounts', parameters('storage-accountName'))]"
      ],
      "properties": {
        "defaultEncryptionScope": "$account-encryption-key",
        "denyEncryptionScopeOverride": false,
        "publicAccess": "None"
      }
    }
  ],
  "outputs": {
  }
}
