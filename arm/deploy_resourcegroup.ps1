<#
	.SYNOPSIS
	リソースグループへのリソースのデプロイを行います

	.DESCRIPTION
	デプロイに以下のファイルを使用します。
	- template.json: メインのテンプレートファイル
	- parameters.${Env}.json: template.jsonに対応するパラメータファイル
	- template_alert.json: アラート設定のテンプレートファイル
	- parameters_alert.${Env}.json: template_alert.jsonに対応するパラメータファイル
	- deploy_settings/${Env}_variables.ps1: 環境設定スクリプト
	- Register-ResourceProviders.ps1: リソース登録スクリプト

	.PARAMETER Env
	対象の環境を指定します。
	指定した環境に対する設定ファイルがdeploy_settingフォルダ内に、
	指定した環境に対するパラメータファイルが同一フォルダ内に必要です。

    .PARAMETER Company
	対象の会社名を指定します。
	指定した会社名に対する設定ファイルがdeploy_settingフォルダ内に、
	指定した会社名に対するパラメータファイルが同一フォルダ内に必要です。

	.OUTPUTS
	ARM テンプレート適用時の出力が出力されます
#>

param (
	# デプロイしたい環境を指定する。指定は必須。
	[Parameter(Mandatory=$true)][string]$Env,
	[Parameter(Mandatory=$true)][string]$Company
)

# 宣言を必須にする
Set-StrictMode -Version Latest

# エラー時にスクリプトを終了させる
$ErrorActionPreference = "Stop"

. "${PSScriptRoot}\deploy_settings\${Env}_variables.ps1"

$clientSecret = ConvertTo-SecureString -String $ClientSecret -AsPlainText -Force
$clientCredential = New-Object System.Management.Automation.PSCredential($ClientId, $clientSecret)

Connect-AzAccount -ServicePrincipal -Credential $clientCredential -Tenant $TenantId

$providers = (
	'Microsoft.Web',
	'Microsoft.ServiceBus',
	'Microsoft.Storage',
	'Microsoft.insights',
	'Microsoft.operationalinsights',
	'Microsoft.Network'
)

<#
	.SYNOPSIS
	ARM テンプレートをデプロイします

	.PARAMETER Message
	処理開始時に出力するメッセージを指定します

	.PARAMETER Suffix
	パラメータ名およびテンプレート名のsuffixを指定します

	.OUTPUTS
	ARM テンプレート適用時の出力が出力されます
#>
function DeployResources {
	param (
		[string]$Message,
		[string]$Suffix,
		[string]$ParameterFileName,
		[string]$TemplateFileName,
		[string]$ResourceGroupName
	)

	$parameterFilePath = "${PSScriptRoot}\${ParameterFileName}"
	$templateFilePath = "${PSScriptRoot}\${TemplateFileName}"

	Write-Output $Message

	# ARMテンプレートの適用
	Write-Host "New-AzResourceGroupDeployment -ResourceGroupName $ResourceGroupName -TemplateFile $templateFilePath -TemplateParameterFile $parameterFilePath"
	$result = New-AzResourceGroupDeployment -ResourceGroupName $ResourceGroupName -TemplateFile $templateFilePath -TemplateParameterFile $parameterFilePath

	# 一旦出力
	$result
}


. ${PSScriptRoot}\Register-ResourceProviders.ps1 -providers $providers

DeployResources -Message "Start common resources deploy!" `
	-ParameterFileName "params_${Env}\parameters_share.${Env}.json" `
	-TemplateFileName "template_share.json" `
	-ResourceGroupName $ResourceGroupNames['share']

DeployResources -Message "Start company resources deploy!" `
	-ParameterFileName "params_${Env}\parameters_${Company}.${Env}.json" `
	-TemplateFileName "template_unique.json" `
	-ResourceGroupName $ResourceGroupNames['unique']

DeployResources -Message "Start common resources depending on company resources deploy!" `
	-ParameterFileName "params_${Env}\parameters_share_dep.${Env}.json" `
	-TemplateFileName "template_share_dep.json" `
	-ResourceGroupName $ResourceGroupNames['share']

$alertEnabled = -not (Get-Variable -Scope Script -Include SkipAlert -ValueOnly)
$frontDoorEnabled = -not (Get-Variable -Scope Script -Include SkipFrontDoor -ValueOnly)

# SkipFrontDoorが定義されていて真を返すならスキップ
if ($frontDoorEnabled) {
	DeployResources -Message "Start deploy of FrontDoor!" `
		-ParameterFileName "params_${Env}\parameters_frontdoor.${Env}.json" `
		-TemplateFileName "template_share_frontdoor.json" `
		-ResourceGroupName $ResourceGroupNames['share']

# SkipAlertが定義されていて真を返すならスキップ
if ($alertEnabled) {
	DeployResources -Message "Start deploy of common AlertSettings!" `
		-ParameterFileName "params_${Env}\parameters_share_alert.${Env}.json" `
		-TemplateFileName "template_share_alert.json" `
		-ResourceGroupName $ResourceGroupNames['share']

    DeployResources -Message "Start deploy of unique AlertSettings!" `
		-ParameterFileName "params_${Env}\parameters_${Company}_alert.${Env}.json" `
		-TemplateFileName "template_unique_alert.json" `
		-ResourceGroupName $ResourceGroupNames['unique']

	if ($frontDoorEnabled) {
		DeployResources -Message "Start deploy of FrontDoor AlertSettings!" `
			-ParameterFileName "params_${Env}\parameters_frontdoor_alert.${Env}.json" `
			-TemplateFileName "template_share_frontdoor_alert.json" `
			-ResourceGroupName $ResourceGroupNames['share']
		}
	}
}
