{
  "$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentParameters.json#",
  "contentVersion": "*******",
  "parameters": {
    "global-location": {
      "value": "Japan East"
    },
    // =====================
    //
    // 個社関連パラメータ
    //
    // =====================

    "global-companies": {
      "value": [
        {
          "companyName": "mec",
          "appService-hostName": "app-attane-dev-mec-002.azurewebsites.net",
          "waf-routingName": "routing-attane-dev-mec",
          "waf-customForwardingPath": "/",
          "waf-patternsToMatch": [ "/mec/", "/mec/*" ],
          "waf-backendPoolName": "backend-attane-dev-mec",
          "waf-loadBalancingName": "loadBalancingSettings-dev-mec",
          "waf-healthProbeName": "healthProbeSettings-dev-mec",
          "waf-frontdoorProbeInterval": 255
        }
      ]
    },

    // =====================
    //
    // Web Application Firewall関連パラメータ
    //
    // =====================
    "waf-enabled": {
      "value": true
    },
    "waf-policyName": {
      "value": "wafattanedev002"
    },
    "waf-frontdoorName": {
      "value": "fd-attane-dev-002"
    },
    "waf-frontdoorDiagnosticName": {
      "value": "diag-fd-attane-dev-002"
    },
    "waf-routingName-default": {
      "value": "routing-attane-dev-default"
    },
    // =====================
    //
    // Application Insights関連パラメータ
    //
    // =====================
    "appInsights-workspaceName": {
      "value": "log-attane-dev-002"
    }
  }
}
