{
  "$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentParameters.json#",
  "contentVersion": "*******",
  "parameters": {
    "global-location": {
      "value": "Japan East"
    },
    // =====================
    //
    // Appservice関連パラメータ
    //
    // =====================
    "appServicePlan-ResourceGroup": {
      "value": "rg-attane-dev-share"
    },
    "automation-ResourceGroup":{
      "value": "rg-attane-dev-unique-mec"
    },
    "appServicePlan-hostingPlanName": {
      "value": "plan-attane-dev-002"
    },
    "appService-siteName": {
      "value": "app-attane-dev-mec-002"
    },
    "appService-hostNames": {
      "value": [
        "app-attane-dev-mec-002.azurewebsites.net"
      ]
    },
    "appService-alwaysOn": {
      "value": true
    },
    "appService-env": {
      "value": "{
        \"ASPNETCORE_ENVIRONMENT\": \"Development\",
        \"AllowedHosts\": \"*\",
        \"AzureAD:Instance\": \"https://login.microsoftonline.com\",
        \"AzureAD:AllowAppIds:0\": \"1fec8e78-bce4-4aaf-ab1b-5451cc387264\",
        \"AzureAD:AllowAppIds:1\": \"5e3ce6c0-2b1f-4285-8d4b-75ee78787346\",
        \"AzureAD:AllowAppIds:2\": \"469c477c-b4cb-42e0-a90c-170f22e2b217\",
        \"AzureAD:Audience\": \"api://dev-mec.fd.attane-stg.com/5373bead-18df-4ae9-8b7e-9ffddaec51ba\",
        \"AzureAD:ClientId\": \"5373bead-18df-4ae9-8b7e-9ffddaec51ba\",
        \"AzureAD:ClientSecret\": \"****************************************\",
        \"AzureAD:Scopes:Graph\": \"https://graph.microsoft.com/.default\",
        \"AzureAD:Scopes:Spo\": \"https://o365mectest.sharepoint.com/.default\",
        \"AzureAD:Scopes:Api\": \"api://dev-mec.fd.attane-stg.com/5373bead-18df-4ae9-8b7e-9ffddaec51ba/user_impersonation\",
        \"AzureAD:TenantId\": \"15d6e921-972f-4a4e-81d6-1ec3d73471a5\",
        \"WEBSITE_DNS_SERVER\": \"*************\",
        \"WEBSITE_VNET_ROUTE_ALL\": \"1\",
        \"ServiceBus:ConnectionString\": \"Endpoint=sb://sb-attane-dev-002.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=0xn/RVfM42sLSFQYbFZmLOd6LKsF9+PvQ+ASbHxGjp8=\",
        \"Geranium:Search:QueueName\": \"search-process-dev-mec-002\"
      }"
    },
    "appService-healthCheckEnabled": {
      "value": true
    },
    "appService-DiagnosticName":{
      "value": "app-attane-dev-mec-002-diag"
    },
    "appService-domainName": {
      "value": "dev-mec.app.attane-stg.com" //適切な値を設定する
    },
    "appService-thumbprint": {
      "value": "C0CDF911D757833B295E72F6FB355380FA337C52" //適切な値を設定する（証明書の設定をしてからでないとわからない）
    },
    "waf-frontdoorName": {
      "value": "fd-attane-dev-002"
    },
    "accessLogs-enabled":{
      "value": true
    },
    "waf-enabled": {
      "value": true
    },
    "waf-frontdoorDiagnosticName": {
      "value": "diag-fd-attane-dev-002"
    },
    // =====================
    //
    // Application Insights関連パラメータ
    //
    // =====================
    "appInsights-workspaceName": {
      "value": "log-attane-dev-002"
    },
    "appInsights-componentsName": {
      "value": "appi-attane-dev-002"
    },
    // =====================
    //
    // Storage関連パラメータ
    //
    // =====================
    "storage-accountName": {
      "value": "stattanedevmec002"
    },
    "storage-accessTier": {
      "value": "Cool"
    },
    // =====================
    //
    // Functions関連パラメータ
    //
    // =====================
    "functions-siteName": {
      "value": "func-attane-dev-mec-002"
    },
    "functions-env": {
      "value": "{
        \"FUNCTIONS_EXTENSION_VERSION\": \"~4\",
        \"FUNCTIONS_WORKER_RUNTIME\": \"node\",
        \"WEBSITE_NODE_DEFAULT_VERSION\": \"~18\",
        \"WEBSITE_RUN_FROM_PACKAGE\": \"1\",
        \"AzureClientId\": \"5373bead-18df-4ae9-8b7e-9ffddaec51ba\",
        \"AzureClientSecret\": \"****************************************\",
        \"WEBSITE_TIME_ZONE\": \"Tokyo Standard Time\",
        \"ServiceBus:fullyQualifiedNamespace\": \"sb-attane-dev-002.servicebus.windows.net\",
        \"SearchProcessQueueName\": \"search-process-dev-mec-002\",
        \"AzureFunctionsJobHost__logging__logLevel__Function\": \"Information\",
        \"AzureTenantId\": \"15d6e921-972f-4a4e-81d6-1ec3d73471a5\",
        \"GraphGroupId\": \"d1e6da81-18f1-46bc-93a8-781a4e62dacc\",
        \"GraphGroupIds\": \"3f160ee7-90ef-4787-92bc-a7bc2a206e49;10c6b883-89a9-485c-8b97-f7fa69f5343d\",
        \"TeamsAppId\": \"5373bead-18df-4ae9-8b7e-9ffddaec51ba\"
        }"
    },
    "functions-ipSecurityEnabled" : {
      "value": true
    },
    "functions-healthCheckEnabled" : {
      "value": true
    },
    // =====================
    //
    // automation関連パラメータ
    //
    // =====================
    "automation-enabled": {
      "value": true
    },
    "automation-accountName": {
      "value": "aa-attane-dev-mec-002"
    },
    "automation-runbookName": {
      "value": "Restart-AppService"
    },
    "automation-artifactsLocation": {
      "value": "https://stattanestg001.blob.core.windows.net/"
    },
    "automation-artifactsPath": {
      "value": "sources/Restart.ps1"
    },
    "automation-artifactsLocationSasToken": {
      "value": "?sp=r&st=2023-10-25T07:45:08Z&se=2023-10-25T15:45:08Z&spr=https&sv=2022-11-02&sr=b&sig=57fUwp2CJxHO5N%2FZfWWyk1HnYkzRbuzk5pEZuTdBP9k%3D"
    },
    "automation-scheduleName": {
      "value": "Daily"
    },
    "automation-jobScheduleName": {
      "value": "0586C0FE-2F55-47A1-89D5-E52E34271D94"
    },
    "automation-scheduleStartTime": {
      "value": "2023-10-26T04:00:00+09:00" // 初期作成時、更新時は15分以上未来の日時である必要がある
    },
    "webAppRoleNameGuid-aa": {
      "value": "35574D28-E985-466A-BC61-A35F227976CB"
    },
    "functionRoleNameGuid-aa": {
      "value": "4A8EA3E5-73F7-4A4F-ACD4-24C32F792924"
    },
    "application-enabled": {
      "value": true
    },
    // =====================
    //
    // ネットワーク関連パラメータ
    //
    // =====================
    "net-vnetEnabled": {
      "value": true
    },
    "vnet-ResourceGroup" : {
      "value" : "rg-attane-dev-share"
    },
    "net-apiEndpointsubnetName": {
      "value": "snet-attane-dev-002"
    },
    "net-vnetName": {
      "value": "vnet-attane-dev-002"
    }
  }
}