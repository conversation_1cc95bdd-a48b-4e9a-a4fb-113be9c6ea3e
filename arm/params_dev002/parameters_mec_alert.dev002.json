{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentParameters.json#", "contentVersion": "1.0.0.0", "parameters": {"global-location": {"value": "Japan East"}, "alert-enabled": {"value": true}, "resourceGroup-Name": {"value": "rg-attane-dev-unique-mec"}, "alert-actionGroupResourceGroupName": {"value": "rg-attane-dev-share"}, "alert-actionGroupsName": {"value": "ag-attane-dev-002"}, "appService-siteName": {"value": "app-attane-dev-mec-002"}, "appService-DataOutAlertsName": {"value": "app-attane-dev-mec-002-over_dataOutSize"}, "appService-http4xxAlertsName": {"value": "app-attane-dev-mec-002-http4xx"}, "appService-http5xxAlertsName": {"value": "app-attane-dev-mec-002-http5xx"}, "appService-responseAlertsName": {"value": "app-attane-dev-mec-002-over_average_responseTime"}, "appService-ResponseTimeAlertsName": {"value": "app-attane-dev-mec-002-over_serveiceResponseTime"}, "functions-siteName": {"value": "func-attane-dev-mec-002"}, "functions-AverageResponseTimeAlert": {"value": "func-attane-dev-mec-002-over_average_responseTime"}, "functions-ResponseTimeAlert": {"value": "func-attane-dev-mec-002-over_serveiceResponseTime"}, "resourceHealth-ActivityLogAlerts": {"value": "rg-attane-dev-unique-mec-serviceHealth"}, "resourceHealth-Alerts": {"value": "rg-attane-dev-unique-mec-resourcesHealth"}, "storage-accountName": {"value": "stattanedevmec002"}, "storage-OverEgressAlertsName": {"value": "stattanedevmec002_over_egress"}, "storage-OverEgressAlertsName-threshold": {"value": *********}, "storage-OverTransactionsAlertsName": {"value": "stattanedevmec002_over_transactions"}, "storage-OverTransactionsAlertsName-threshold": {"value": 100000}, "storage-SuccessServerLatencyAlertsName": {"value": "stattanedevmec002-over_serveiceRequestsTime"}, "storage-SuccessServerLatencyAlertsName-threshold": {"value": 100}, "storage-UsedCapacityAlertsName": {"value": "stattanedevmec002-over_usedCapacity"}}}