{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentParameters.json#", "contentVersion": "*******", "parameters": {"global-location": {"value": "Japan East"}, "resourceGroup-Name": {"value": "rg-attane-dev-share"}, "alert-enabled": {"value": true}, "alert-actionGroupsName": {"value": "ag-attane-dev-002"}, "alert-actionGroupShortName": {"value": "ag-dev-002"}, "alert-emailReceivers": {"value": [{"name": "O365_miTene開発検証用(MJIT)_-EmailAction-", "emailAddress": "<EMAIL>", "useCommonAlertSchema": false}]}, "appInsights-componentsName": {"value": "appi-attane-dev-002"}, "net-publicIpName": {"value": "pip-attane-dev-002"}, "appServicePlan-hostingPlanName": {"value": "plan-attane-dev-002"}, "serviceBus-namespace": {"value": "sb-attane-dev-002"}, "appServicePlan-cpuPercintageAlertsName": {"value": "plan-attane-dev-002-over_cpu_percentage"}, "appServicePlan-memoryPercentageAlertsName": {"value": "plan-attane-dev-002-over_memory_percentage"}, "publicIP-ddosAlertsName": {"value": "pip-attane-dev-002-ddos"}, "serviceBus-queueCountAlertsName": {"value": "sb-attane-dev-002-queueCount"}, "resourceHealth-Alerts": {"value": "rg-attane-dev-share-resourcesHealth"}, "appInsights-unhandledExceptionAlertsName": {"value": "appi-attane-dev-002-unhandledException"}, "resourceHealth-ActivityLogAlerts": {"value": "rg-attane-dev-share-serviceHealth"}, "appInsights-callerUnmatchAlertsName": {"value": "app-attane-dev-mec-002-callerUnmatch"}, "appInsights-tooManyRetryAlertsName": {"value": "app-attane-dev-mec-002-tooManyRetry"}, "appInsights-authErrorAlertsName": {"value": "app-attane-dev-mec-002-authError"}, "appInsights-missingEnvValuesAlertsName": {"value": "app-attane-dev-mec-002-missingEnvValues"}, "appInsights-unautorizedAppAlertsName": {"value": "app-attane-dev-mec-002-unautorizedApp"}, "appInsights-apiCallErrorAlertsName": {"value": "app-attane-dev-mec-002-apiCallError"}, "appInsights-functionsUnrecoverableErrorAlertsName": {"value": "func-attane-dev-mec-002-UnrecoverableError"}, "appInsights-functionsRecoverableErrorAlertsName": {"value": "func-attane-dev-mec-002-RecoverableError"}, "logAnalyticsWorkspace-componentsName": {"value": "log-attane-dev-002"}, "logAnalyticsWorkspace-overAdditionalAmountName": {"value": "log-attane-dev-002_over_additional_amount"}, "logAnalyticsWorkspace-overAdditionalAmountName-threshold": {"value": 1}}}