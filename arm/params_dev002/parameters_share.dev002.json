{
  "$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentParameters.json#",
  "contentVersion": "*******",
  "parameters": {
    "global-location": {
      "value": "Japan East"
    },
    // =====================
    //
    // AppservicePlan関連パラメータ
    //
    // =====================
    "appServicePlan-hostingPlanName": {
      "value": "plan-attane-dev-002"
    },
    "appServicePlan-workerSize": {
      "value": "0"
    },
    "appServicePlan-workerSizeId": {
      "value": "0"
    },
    "appServicePlan-numberOfWorkers": {
      "value": "1"
    },
    "appServicePlan-maximumElasticWorkerCount": {
      "value": "1"
    },
    "appServicePlan-sku": {
      "value": "Standard"
    },
    "appServicePlan-skuCode": {
      "value": "S2"
    },
    "appServicePlan-capacity": {
      "value": "1"
    },
    "autoScale-enabled": {
      "value": true
    },
    "application-enabled": {
      "value": true
    },
    "appServicePlan-autoScale": {
      "value": "planScale-attane-dev-002"
    },
    // =====================
    //
    // Application Insights関連パラメータ
    //
    // =====================
    "appInsights-workspaceName": {
      "value": "log-attane-dev-002"
    },
    "appInsights-workspaceType": {
      "value": "pergb2018"
    },
    "appInsights-componentsName": {
      "value": "appi-attane-dev-002"
    },
    "appInsights-retentionInDays": {
      "value": 730
    },
    // =====================
    //
    // サービスバス関連パラメータ
    //
    // =====================
    "serviceBus-namespace":{
      "value": "sb-attane-dev-002"
    },
    "serviceBus-skuName":{
      "value": "Standard"
    },
    // =====================
    //
    // ネットワーク関連パラメータ
    //
    // =====================
    "net-vnetEnabled": {
      "value": true
    },
    "net-vnetName": {
      "value": "vnet-attane-dev-002"
    },
    "net-vnet-addressPrefixes": {
      "value": "10.0.0.0/8"
    },
    "net-apiEndpointsubnetName": {
      "value": "snet-attane-dev-002"
    },
    "net-subnet-addressPrefixes": {
      "value": "10.0.0.0/16"
    },
    "net-natGwName": {
      "value": "ng-attane-dev-002"
    },
    "net-publicIpName": {
      "value": "pip-attane-dev-002"
    }
  }
}
