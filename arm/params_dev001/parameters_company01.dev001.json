{
  "$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentParameters.json#",
  "contentVersion": "*******",
  "parameters": {
    "global-location": {
      "value": "Japan East"
    },
    "appServicePlan-ResourceGroup": {
      "value": "rg-geranium-attane-dev001"
    },
    "automation-ResourceGroup":{
      "value": "rg-geranium-attane-dev001"
    },
    "appServicePlan-hostingPlanName": {
      "value": "plan-attane-dev1-001"
    },
    "appService-siteName": {
      "value": "app-attane-dev1-001"
    },
    "appService-hostNames": {
      "value": [
        "app-attane-dev1-001.azurewebsites.net"
      ]
    },
    "appService-alwaysOn": {
      "value": true
    },
    "appService-env": {
      "value": "{
        \"ASPNETCORE_ENVIRONMENT\": \"AppDevelopment\",
        \"AllowedHosts\": \"*\",
        \"AzureAD:Instance\": \"https://login.microsoftonline.com\",
        \"AzureAD:AllowAppIds:0\": \"1fec8e78-bce4-4aaf-ab1b-5451cc387264\",
        \"AzureAD:AllowAppIds:1\": \"5e3ce6c0-2b1f-4285-8d4b-75ee78787346\",
        \"AzureAD:AllowAppIds:2\": \"469c477c-b4cb-42e0-a90c-170f22e2b217\",
        \"AzureAD:AllowAppIds:3\": \"07f0a107-95c1-41ad-8f13-912eab68b93f\",
        \"AzureAD:Audience\": \"api://app-attane-dev1-001.azurewebsites.net/bfc8002a-da84-469e-b415-dab9bca57177\",
        \"AzureAD:ClientId\": \"bfc8002a-da84-469e-b415-dab9bca57177\",
        \"AzureAD:ClientSecret\": \"****************************************\",
        \"AzureAD:Scopes:Graph\": \"https://graph.microsoft.com/.default\",
        \"AzureAD:Scopes:Spo\": \"https://projectgeranium.sharepoint.com/.default\",
        \"AzureAD:Scopes:Api\": \"api://app-attane-dev1-001.azurewebsites.net/bfc8002a-da84-469e-b415-dab9bca57177/user_impersonation\",
        \"AzureAD:TenantId\": \"00421d45-dcb9-4f93-a14f-62a8884dc9a8\",
        \"WEBSITE_DNS_SERVER\": \"*************\",
        \"WEBSITE_VNET_ROUTE_ALL\": \"1\",
        \"ServiceBus:ConnectionString\": \"Endpoint=sb://sb-attane-dev1-001.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=yoL6Ip2kbdvu7eN2k8VLNKGMm63CqXoBH+ASbDtDEIo=\",
        \"Geranium:Search:QueueName\": \"search-process\"
      }"
    },
    "appService-healthCheckEnabled": {
      "value": false
    },
    "appService-DiagnosticName":{
      "value": "app-attane-dev1-001-diag"
    },
    "waf-frontdoorName": {
      "value": "fd-attane-dev1-001"
    },
    "accessLogs-enabled":{
      "value": true
    },
    "waf-enabled": {
      "value": false
    },
    "waf-frontdoorDiagnosticName": {
      "value": "diag-fd-attane-dev1-001"
    },
    "appInsights-workspaceName": {
      "value": "log-attane-dev1-001"
    },
    "appInsights-componentsName": {
      "value": "appi-attane-dev1-001"
    },
    "storage-accountName": {
      "value": "stattanedev1001"
    },
    "storage-accessTier": {
      "value": "Cool"
    },
    "functions-siteName": {
      "value": "func-attane-dev1-001"
    },
    "functions-env": {
      "value": "{
        \"FUNCTIONS_EXTENSION_VERSION\": \"~4\",
        \"FUNCTIONS_WORKER_RUNTIME\": \"node\",
        \"WEBSITE_NODE_DEFAULT_VERSION\": \"~18\",
        \"WEBSITE_RUN_FROM_PACKAGE\": \"1\",
        \"AzureClientId\": \"bfc8002a-da84-469e-b415-dab9bca57177\",
        \"AzureClientSecret\": \"****************************************\",
        \"WEBSITE_TIME_ZONE\": \"Tokyo Standard Time\",
        \"ServiceBus:fullyQualifiedNamespace\": \"sb-attane-dev1-001.servicebus.windows.net\",
        \"SearchProcessQueueName\": \"search-process\",
        \"AzureFunctionsJobHost__logging__logLevel__Function\": \"Trace\"
    }"
    },
    "functions-ipSecurityEnabled" : {
      "value": false
    },
    "functions-healthCheckEnabled" : {
      "value": false
    },
    "automation-enabled": {
      "value": false
    },
    "automation-accountName": {
      "value": "aa-attane-dev1-001"
    },
    "automation-runbookName": {
      "value": "Restart-AppService"
    },
    "automation-artifactsLocation": {
      "value": "https://stattanedev1001.blob.core.windows.net/"
    },
    "automation-artifactsPath": {
      "value": "artifacts/Restart.ps1"
    },
    "automation-artifactsLocationSasToken": {
      "value": "?sp=r&st=2023-01-13T03:02:37Z&se=2023-01-13T11:02:37Z&spr=https&sv=2021-06-08&sr=b&sig=35RFWRhJr4kPkZ9Pwzky3nDJQajaMONOHQxmyNjJ57U%3D"
    },
    "automation-scheduleName": {
      "value": "Daily"
    },
    "automation-jobScheduleName": {
      "value": "97F53B25-3CE1-4650-84C4-96B20F312EBE"
    },
    //"automation-scheduleStartTime": {
    //  "value": "2022-07-19T13:00:00+09:00" // 初期作成時、更新時は15分以上未来の日時である必要がある
    //},
    "webAppRoleNameGuid-aa": { //使わないのでDev002と同じ値
      "value": "F845F44D-DCE0-4E9E-8A0B-E69B0889CAB3"
    },
    "functionRoleNameGuid-aa": {//使わないのでDev002と同じ値
      "value": "A7AB174A-F47F-476E-B72D-7356E966E549"
    },
    "net-vnetEnabled": {
      "value": false
    },
    "vnet-ResourceGroup" : {
      "value" : "rg-geranium-attane-dev001"
    },
    "net-apiEndpointsubnetName": {
      "value": "snet-attane-dev1-001"
    },
    "application-enabled": {
      "value": true
    },
    "net-vnetName": {
      "value": "vnet-attane-dev1-001"
    },
    "net-vnetIpRules": {
      "value": [
        {
          "value": "**************",
          "action": "Allow"
        },
        {
          "value": "**************",
          "action": "Allow"
        },
        {
          "value": "************",
          "action": "Allow"
        },
        {
          "value": "*********",
          "action": "Allow"
        },
        {
            "value": "*************",
            "action": "Allow"
        },
        {
            "value": "*************",
            "action": "Allow"
        },
        {
            "value": "*************",
            "action": "Allow"
        },
        {
            "value": "************",
            "action": "Allow"
        },
        {
            "value": "**************",
            "action": "Allow"
        },
        {
            "value": "*************",
            "action": "Allow"
        },
        {
            "value": "************",
            "action": "Allow"
        },
        {
            "value": "184.72.47.199",
            "action": "Allow"
        },
        {
            "value": "54.193.251.180",
            "action": "Allow"
        },
        {
            "value": "54.241.31.130",
            "action": "Allow"
        },
        {
            "value": "13.52.27.189",
            "action": "Allow"
        },
        {
            "value": "13.52.105.217",
            "action": "Allow"
        },
        {
            "value": "13.52.157.154",
            "action": "Allow"
        },
        {
            "value": "13.52.175.228",
            "action": "Allow"
        },
        {
            "value": "52.52.50.152",
            "action": "Allow"
        },
        {
            "value": "52.52.110.223",
            "action": "Allow"
        },
        {
            "value": "50.18.117.136",
            "action": "Allow"
        },
        {
            "value": "54.215.44.246",
            "action": "Allow"
        }
      ]
    }
  }
}
