{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentParameters.json#", "contentVersion": "*******", "parameters": {"global-location": {"value": "Japan East"}, "appServicePlan-hostingPlanName": {"value": "plan-attane-dev1-001"}, "appServicePlan-workerSize": {"value": "0"}, "appServicePlan-workerSizeId": {"value": "0"}, "appServicePlan-numberOfWorkers": {"value": "3"}, "appServicePlan-maximumElasticWorkerCount": {"value": "5"}, "appServicePlan-sku": {"value": "Basic"}, "appServicePlan-skuCode": {"value": "B2"}, "appServicePlan-capacity": {"value": "2"}, "appInsights-workspaceName": {"value": "log-attane-dev1-001"}, "appInsights-workspaceType": {"value": "pergb2018"}, "appInsights-componentsName": {"value": "appi-attane-dev1-001"}, "appInsights-retentionInDays": {"value": 30}, "autoScale-enabled": {"value": false}, "appServicePlan-autoScale": {"value": "plan-attane-dev1-001"}, "application-enabled": {"value": true}, "serviceBus-namespace": {"value": "sb-attane-dev1-001"}, "serviceBus-skuName": {"value": "Standard"}, "net-vnetEnabled": {"value": false}, "net-vnetName": {"value": "vnet-attane-dev1-001"}, "net-vnet-addressPrefixes": {"value": "***********/16"}, "net-apiEndpointsubnetName": {"value": "snet-attane-dev1-001"}, "net-subnet-addressPrefixes": {"value": "***********/24"}, "net-natGwName": {"value": "ng-attane-dev1-001"}, "net-publicIpName": {"value": "pip-attane-dev1-001"}}}