{
  "$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentParameters.json#",
  "contentVersion": "*******",
  "parameters": {
    "global-location": {
      "value": "Japan East"
    },
    // =====================
    //
    // AppservicePlan関連パラメータ
    //
    // =====================
    "appServicePlan-hostingPlanName": {
      "value": "plan-attane-prod-001"
    },
    "appServicePlan-workerSize": {
      "value": "0"
    },
    "appServicePlan-workerSizeId": {
      "value": "0"
    },
    "appServicePlan-numberOfWorkers": {
      "value": "10"
    },
    "appServicePlan-maximumElasticWorkerCount": {
      "value": "20"
    },
    "appServicePlan-sku": {
      "value": "PremiumV3"
    },
    "appServicePlan-skuCode": {
      "value": "P1v3"
    },
    "appServicePlan-capacity": {
      "value": "2"
    },
    "autoScale-enabled": {
      "value": true
    },
    "application-enabled": {
      "value": true
    },
    "appServicePlan-autoScale": {
      "value": "planScale-attane-prod-001"
    },
    // =====================
    //
    // Application Insights関連パラメータ
    //
    // =====================
    "appInsights-workspaceName": {
      "value": "log-attane-prod-001"
    },
    "appInsights-workspaceType": {
      "value": "pergb2018"
    },
    "appInsights-componentsName": {
      "value": "appi-attane-prod-001"
    },
    "appInsights-retentionInDays": {
      "value": 730
    },
    // =====================
    //
    // サービスバス関連パラメータ
    //
    // =====================
    "serviceBus-namespace":{
      "value": "sb-attane-prod-001"
    },
    "serviceBus-skuName":{
      "value": "Standard"
    },
    // =====================
    //
    // ネットワーク関連パラメータ
    //
    // =====================
    "net-vnetEnabled": {
      "value": true
    },
    "net-vnetName": {
      "value": "vnet-attane-prod-001"
    },
    "net-vnet-addressPrefixes": {
      "value": "10.0.0.0/8"
    },
    "net-apiEndpointsubnetName": {
      "value": "snet-attane-prod-001"
    },
    "net-subnet-addressPrefixes": {
      "value": "10.0.0.0/16"
    },
    "net-natGwName": {
      "value": "ng-attane-prod-001"
    },
    "net-publicIpName": {
      "value": "pip-attane-prod-001"
    }
  }
}
