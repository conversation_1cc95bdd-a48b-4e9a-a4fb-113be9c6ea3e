{
  "$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentParameters.json#",
  "contentVersion": "*******",
  "parameters": {
    "global-location": {
      "value": "Japan East"
    },
    // =====================
    //
    // Appservice関連パラメータ
    //
    // =====================
    "appServicePlan-ResourceGroup": {
      "value": "rg-attane-prod-share"
    },
    "automation-ResourceGroup":{
      "value": "rg-attane-prod-unique-mec"
    },
    "appServicePlan-hostingPlanName": {
      "value": "plan-attane-prod-001"
    },
    "appService-siteName": {
      "value": "app-attane-prod-mec-001"
    },
    "appService-hostNames": {
      "value": [
        "app-attane-prod-mec-001.azurewebsites.net"
      ]
    },
    "appService-alwaysOn": {
      "value": true
    },
    "appService-env": {
      "value": "{
        \"ASPNETCORE_ENVIRONMENT\": \"Production\",
        \"AllowedHosts\": \"*\",
        \"AzureAD:Instance\": \"https://login.microsoftonline.com\",
        \"AzureAD:AllowAppIds:0\": \"1fec8e78-bce4-4aaf-ab1b-5451cc387264\",
        \"AzureAD:AllowAppIds:1\": \"5e3ce6c0-2b1f-4285-8d4b-75ee78787346\",
        \"AzureAD:AllowAppIds:2\": \"469c477c-b4cb-42e0-a90c-170f22e2b217\",
        \"AzureAD:Audience\": \"api://fd-attane-prod-001.azurefd.net/3ddaf1bf-e981-4532-93d6-b6098d70f421\",
        \"AzureAD:ClientId\": \"3ddaf1bf-e981-4532-93d6-b6098d70f421\",
        \"AzureAD:ClientSecret\": \"****************************************\",
        \"AzureAD:Scopes:Graph\": \"https://graph.microsoft.com/.default\",
        \"AzureAD:Scopes:Spo\": \"https://o365mec.sharepoint.com/.default\",
        \"AzureAD:Scopes:Api\": \"api://fd-attane-prod-001.azurefd.net/3ddaf1bf-e981-4532-93d6-b6098d70f421/user_impersonation\",
        \"AzureAD:TenantId\": \"47f8fdbc-0526-4967-9f15-f248705ca58a\",
        \"WEBSITE_DNS_SERVER\": \"*************\",
        \"WEBSITE_VNET_ROUTE_ALL\": \"1\",
        \"ServiceBus:ConnectionString\": \"{{TODO: ここに接続文字列を設定する}}\",
        \"Geranium:Search:QueueName\": \"search-process-prod-mec-001\"
      }"
    },
    "appService-healthCheckEnabled": {
      "value": true
    },
    "appService-DiagnosticName":{
      "value": "app-attane-prod-mec-001-diag"
    },
    "waf-frontdoorName": {
      "value": "fd-attane-prod-001"
    },
    "accessLogs-enabled":{
      "value": true
    },
    "waf-enabled": {
      "value": true
    },
    "waf-frontdoorDiagnosticName": {
      "value": "diag-fd-attane-prod-001"
    },
    // =====================
    //
    // Application Insights関連パラメータ
    //
    // =====================
    "appInsights-workspaceName": {
      "value": "log-attane-prod-001"
    },
    "appInsights-componentsName": {
      "value": "appi-attane-prod-001"
    },
    // =====================
    //
    // Storage関連パラメータ
    //
    // =====================
    "storage-accountName": {
      "value": "stattaneprodmec001"
    },
    "storage-accessTier": {
      "value": "Cool"
    },
    // =====================
    //
    // Functions関連パラメータ
    //
    // =====================
    "functions-siteName": {
      "value": "func-attane-prod-mec-001"
    },
    "functions-env": { //TODO:個社毎にキューの名前を変更
      "value": "{
        \"FUNCTIONS_EXTENSION_VERSION\": \"~4\",
        \"FUNCTIONS_WORKER_RUNTIME\": \"node\",
        \"WEBSITE_NODE_DEFAULT_VERSION\": \"~18\",
        \"WEBSITE_RUN_FROM_PACKAGE\": \"1\",
        \"AzureClientId\": \"3ddaf1bf-e981-4532-93d6-b6098d70f421\",
        \"AzureClientSecret\": \"****************************************\",
        \"WEBSITE_TIME_ZONE\": \"Tokyo Standard Time\",
        \"ServiceBus:fullyQualifiedNamespace\": \"sb-attane-prod-001.servicebus.windows.net\",
        \"SearchProcessQueueName\": \"search-process-prod-mec-001\",
        \"AzureFunctionsJobHost__logging__logLevel__Function\": \"Information\"
      },"
    },
    "functions-ipSecurityEnabled" : {
      "value": true
    },
    "functions-healthCheckEnabled" : {
      "value": true
    },
    // =====================
    //
    // automation関連パラメータ
    //
    // =====================
    "automation-enabled": {
      "value": true
    },
    "automation-accountName": {
      "value": "aa-attane-prod-mec-001"
    },
    "automation-runbookName": {
      "value": "Restart-AppService"
    },
    "automation-artifactsLocation": {
      "value": "https://stattaneprod001.blob.core.windows.net/"
    },
    "automation-artifactsPath": {
      "value": "sources/Restart.ps1"
    },
    "automation-artifactsLocationSasToken": {
      "value": "?sp=r&st=2023-02-10T09:45:49Z&se=2023-02-15T14:59:59Z&spr=https&sv=2021-06-08&sr=b&sig=DgpQsyK7JCK8GICPTUtuR90ducFwV81yun8%2FlbPAZ8Q%3D"
    },
    "automation-scheduleName": {
      "value": "Daily"
    },
    "automation-jobScheduleName": {
      "value": "9B54F809-C0E1-4C3D-9BDA-474701D238BD"
    },
    "automation-scheduleStartTime": {
      "value": "2023-2-12T04:00:00+09:00" // 初期作成時、更新時は15分以上未来の日時である必要がある
    },
    "webAppRoleNameGuid-aa": {
      "value": "B41B5BAB-09AE-49DE-930B-9E010727DD06"
    },
    "functionRoleNameGuid-aa": {
      "value": "BF3982C0-D309-4EA8-BEC3-1FB86D70869F"
    },
    "application-enabled": {
      "value": true
    },
    // =====================
    //
    // ネットワーク関連パラメータ
    //
    // =====================
    "net-vnetEnabled": {
      "value": true
    },
    "vnet-ResourceGroup" : {
      "value" : "rg-attane-prod-share"
    },
    "net-apiEndpointsubnetName": {
      "value": "snet-attane-prod-001"
    },
    "net-vnetName": {
      "value": "vnet-attane-prod-001"
    }
  }
}
