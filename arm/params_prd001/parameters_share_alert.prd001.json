{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentParameters.json#", "contentVersion": "*******", "parameters": {"global-location": {"value": "Japan East"}, "resourceGroup-Name": {"value": "rg-attane-prod-share"}, "alert-enabled": {"value": true}, "alert-actionGroupsName": {"value": "ag-attane-prod-001"}, "alert-actionGroupSortName": {"value": "ag-prod-001"}, "alert-emailReceivers": {"value": [{"name": "O365_miTene保守窓口(MJIT)_-EmailAction-", "emailAddress": "<EMAIL>", "useCommonAlertSchema": false}]}, "appInsights-componentsName": {"value": "appi-attane-prod-001"}, "net-publicIpName": {"value": "pip-attane-prod-001"}, "appServicePlan-hostingPlanName": {"value": "plan-attane-prod-001"}, "serviceBus-namespace": {"value": "sb-attane-prod-001"}, "appServicePlan-cpuPercintageAlertsName": {"value": "alert-attane-prod-plan-over_cpu_percentage"}, "appServicePlan-memoryPercentageAlertsName": {"value": "alert-attane-prod-plan-over_memory_percentage"}, "publicIP-ddosAlertsName": {"value": "alert-attane-prod-ddos"}, "serviceBus-queueCountAlertsName": {"value": "alert-attane-prod-queueCount"}, "resourceHealth-Alerts": {"value": "alert-attane-prod-rg-resourcesHealth"}, "appInsights-unhandledExceptionAlertsName": {"value": "alert-attane-prod-unhandledException"}, "resourceHealth-ActivityLogAlerts": {"value": "alert-attane-prod-rg-serviceHealth"}, "appInsights-callerUnmatchAlertsName": {"value": "alert-attane-prod-mec-ase-callerUnmatch"}, "appInsights-tooManyRetryAlertsName": {"value": "alert-attane-prod-mec-ase-tooManyRetry"}, "appInsights-authErrorAlertsName": {"value": "alert-attane-prod-mec-ase-authError"}, "appInsights-missingEnvValuesAlertsName": {"value": "alert-attane-prod-mec-ase-missingEnvValues"}, "appInsights-unautorizedAppAlertsName": {"value": "alert-attane-prod-mec-ase-unautorizedApp"}, "appInsights-apiCallErrorAlertsName": {"value": "alert-attane-prod-mec-ase-apiCallError"}, "appInsights-functionsUnrecoverableErrorAlertsName": {"value": "alert-attane-prod-mec-func-UnrecoverableError"}, "appInsights-functionsRecoverableErrorAlertsName": {"value": "alert-attane-prod-mec-func-RecoverableError"}}}