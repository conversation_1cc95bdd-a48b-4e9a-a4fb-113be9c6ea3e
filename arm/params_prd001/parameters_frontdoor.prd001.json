{
  "$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentParameters.json#",
  "contentVersion": "*******",
  "parameters": {
    "global-location": {
      "value": "Japan East"
    },
    // =====================
    //
    // 個社関連パラメータ
    //
    // =====================

    "global-companies": {
      "value": [
        {
          "companyName": "mec",
          "appService-hostName": "app-attane-prod-mec-001.azurewebsites.net",
          "waf-routingName": "routing-attane-prod-mec",
          "waf-customForwardingPath": "/",
          "waf-patternsToMatch": [ "/mec/", "/mec/*" ],
          "waf-backendPoolName": "backend-attane-prod-mec",
          "waf-loadBalancingName": "loadBalancingSettings-prod-mec",
          "waf-healthProbeName": "healthProbeSettings-prod-mec",
          "waf-frontdoorProbeInterval": 255
        }
      ]
    },

    // =====================
    //
    // Web Application Firewall関連パラメータ
    //
    // =====================
    "waf-enabled": {
      "value": true
    },
    "waf-policyName": {
      "value": "wafattaneprod001"
    },
    "waf-frontdoorName": {
      "value": "fd-attane-prod-001"
    },
    "waf-frontdoorDiagnosticName": {
      "value": "diag-fd-attane-prod-001"
    },
    "waf-routingName-default": {
      "value": "routing-attane-prod-default"
    },
    // =====================
    //
    // Application Insights関連パラメータ
    //
    // =====================
    "appInsights-workspaceName": {
      "value": "log-attane-prod-001"
    }
  }
}
