{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentParameters.json#", "contentVersion": "1.0.0.0", "parameters": {"global-location": {"value": "Japan East"}, "alert-enabled": {"value": true}, "resourceGroup-Name": {"value": "rg-attane-prod-unique-mec"}, "alert-actionGroupResourceGroupName": {"value": "rg-attane-prod-share"}, "alert-actionGroupsName": {"value": "ag-attane-prod-001"}, "appService-siteName": {"value": "app-attane-prod-001"}, "appService-DataOutAlertsName": {"value": "alert-attane-prod-ase-over_dataOutSize"}, "appService-http4xxAlertsName": {"value": "alert-attane-prod-mec-ase-http4xx"}, "appService-http5xxAlertsName": {"value": "alert-attane-prod-mec-ase-http5xx"}, "resourceHealth-ActivityLogAlerts": {"value": "alert-attane-prod-mec-rg-serviceHealth"}, "resourceHealth-Alerts": {"value": "alert-attane-prod-mec-rg-resourcesHealth"}, "storage-UsedCapacityAlertsName": {"value": "alert-attane-prod-mec-st-over_usedCapacity"}, "storage-accountName": {"value": "stattaneprodmec001"}}}