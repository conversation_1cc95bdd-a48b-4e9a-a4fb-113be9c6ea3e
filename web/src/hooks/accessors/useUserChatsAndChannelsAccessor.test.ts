import { renderHook, act } from '@testing-library/react-hooks';
import useUserChatsAndChannelsAccessor, {
  IUserChatItem,
  IPaginatedResult,
  IUserTeam,
} from './useUserChatsAndChannelsAccessor';
import { WeakTokenProvider } from '../../types/TokenProvider';
import { initGraphClient } from './useGraphApiAccessor';

// Graph 初期化をモック
jest.mock('./useGraphApiAccessor', () => ({
  UseGraphApiError: {
    TOKEN_PROVIDER_NOT_AVAILABLE: 'TOKEN_PROVIDER_NOT_AVAILABLE',
  },
  initGraphClient: jest.fn(),
}));

const mockInitGraphClient = initGraphClient as unknown as jest.Mock;

describe('useUserChatsAndChannelsAccessor', () => {
  const mockTokenProvider: WeakTokenProvider = jest.fn().mockResolvedValue('mock-token');

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('ページネーション機能のテスト', () => {
    // チャットのページネーション取得テスト
    it('チャットのページネーション取得が正しく動作する', async () => {
      const mockChatsResponse = {
        value: [
          {
            id: 'chat1',
            topic: 'テストチャット1',
            chatType: 'oneOnOne',
            members: [
              { displayName: '田中太郎', id: 'user1' },
              { displayName: '佐藤花子', id: 'user2' },
            ],
          },
          {
            id: 'chat2',
            topic: 'テストチャット2',
            chatType: 'group',
            members: [
              { displayName: '山田次郎', id: 'user3' },
              { displayName: '鈴木三郎', id: 'user4' },
            ],
          },
        ],
        '@odata.nextLink': 'https://graph.microsoft.com/v1.0/me/chats?$skiptoken=next',
      };

      // チーム用の空レスポンス
      const mockTeamsResponse = {
        value: [],
        '@odata.nextLink': undefined,
      };

      const mockClient = {
        api: jest.fn().mockReturnThis(),
        expand: jest.fn().mockReturnThis(),
        orderby: jest.fn().mockReturnThis(),
        top: jest.fn().mockReturnThis(),
        get: jest.fn()
          .mockResolvedValueOnce(mockChatsResponse) // チャット取得
          .mockResolvedValueOnce(mockTeamsResponse), // チーム取得
      };

      mockInitGraphClient.mockReturnValue(mockClient);

      const { result } = renderHook(() => useUserChatsAndChannelsAccessor(mockTokenProvider));

      let paginatedResult: IPaginatedResult<IUserChatItem> | undefined;
      await act(async () => {
        if (result.current.fetchUserChatsAndChannelsPaginated) {
          paginatedResult = await result.current.fetchUserChatsAndChannelsPaginated();
        }
      });

      // 結果の検証
      expect(paginatedResult).toBeDefined();
      expect(paginatedResult!.items).toHaveLength(2);
      expect(paginatedResult!.hasMore).toBe(true);
      expect(paginatedResult!.nextPageToken).toBe('https://graph.microsoft.com/v1.0/me/chats?$skiptoken=next');

      // 1件目のデータ
      expect(paginatedResult!.items[0]).toEqual({
        id: 'chat1',
        name: 'テストチャット1',
        type: 'チャット',
        chatType: 'oneOnOne',
      });

      // 2件目のデータ
      expect(paginatedResult!.items[1]).toEqual({
        id: 'chat2',
        name: 'テストチャット2',
        type: 'チャット',
        chatType: 'group',
      });

      // APIが正しく呼ばれることを確認
      expect(mockClient.api).toHaveBeenCalledWith('/me/chats');
      expect(mockClient.expand).toHaveBeenCalledWith('members');
      expect(mockClient.orderby).toHaveBeenCalledWith('lastMessagePreview/createdDateTime desc');
      expect(mockClient.top).toHaveBeenCalledWith(20);
    });

    // 次のページを取得するテスト
    it('nextPageTokenを使用して次のページを取得する', async () => {
      const mockNextPageChatsResponse = {
        value: [
          {
            id: 'chat3',
            topic: 'テストチャット3',
            chatType: 'group',
            members: [
              { displayName: '田中太郎', id: 'user1' },
            ],
          },
        ],
        '@odata.nextLink': undefined, // 最後のページ
      };

      // チーム用の空レスポンス
      const mockTeamsResponse = {
        value: [],
        '@odata.nextLink': undefined,
      };

      const mockClient = {
        api: jest.fn().mockReturnThis(),
        get: jest.fn()
          .mockResolvedValueOnce(mockNextPageChatsResponse) // チャット取得
          .mockResolvedValueOnce(mockTeamsResponse), // チーム取得
      };

      mockInitGraphClient.mockReturnValue(mockClient);

      const { result } = renderHook(() => useUserChatsAndChannelsAccessor(mockTokenProvider));

      let paginatedResult: IPaginatedResult<IUserChatItem> | undefined;
      await act(async () => {
        if (result.current.fetchUserChatsAndChannelsPaginated) {
          paginatedResult = await result.current.fetchUserChatsAndChannelsPaginated(
            'https://graph.microsoft.com/v1.0/me/chats?$skip=20',
          );
        }
      });

      // 結果の検証
      expect(paginatedResult).toBeDefined();
      expect(paginatedResult!.items).toHaveLength(1);
      expect(paginatedResult!.hasMore).toBe(false);
      expect(paginatedResult!.nextPageToken).toBeUndefined();

      // データの検証
      expect(paginatedResult!.items[0]).toEqual({
        id: 'chat3',
        name: 'テストチャット3',
        type: 'チャット',
        chatType: 'group',
      });

      // 次のページのAPIが正しく呼ばれることを確認
      expect(mockClient.api).toHaveBeenCalledWith('https://graph.microsoft.com/v1.0/me/chats?$skip=20');
    });

    // カスタムページサイズのテスト
    it('カスタムページサイズで取得できる', async () => {
      const mockChatsResponse = {
        value: [
          {
            id: 'chat1',
            topic: 'テストチャット1',
            chatType: 'oneOnOne',
            members: [
              { displayName: '田中太郎', id: 'user1' },
            ],
          },
        ],
        '@odata.nextLink': undefined,
      };

      // チーム用の空レスポンス
      const mockTeamsResponse = {
        value: [],
        '@odata.nextLink': undefined,
      };

      const mockClient = {
        api: jest.fn().mockReturnThis(),
        expand: jest.fn().mockReturnThis(),
        orderby: jest.fn().mockReturnThis(),
        top: jest.fn().mockReturnThis(),
        get: jest.fn()
          .mockResolvedValueOnce(mockChatsResponse) // チャット取得
          .mockResolvedValueOnce(mockTeamsResponse), // チーム取得
      };

      mockInitGraphClient.mockReturnValue(mockClient);

      const { result } = renderHook(() => useUserChatsAndChannelsAccessor(mockTokenProvider));

      await act(async () => {
        if (result.current.fetchUserChatsAndChannelsPaginated) {
          await result.current.fetchUserChatsAndChannelsPaginated(undefined, 50);
        }
      });

      // カスタムページサイズが使用されることを確認
      expect(mockClient.top).toHaveBeenCalledWith(50);
    });
  });

  describe('チーム・チャネルページネーション機能のテスト', () => {
    // fetchUserTeamsAndChannelsPaginatedImpl用のテスト
    it('チーム・チャネルのページネーション取得が正しく動作する', async () => {
      const mockTeamsResponse = {
        value: [
          {
            id: 'team1',
            displayName: 'テストチーム1',
          },
          {
            id: 'team2',
            displayName: 'テストチーム2',
          },
        ],
        '@odata.nextLink': 'https://graph.microsoft.com/v1.0/me/joinedTeams?$skip=2',
      };

      const mockChannelsResponse = {
        value: [
          {
            id: 'channel1',
            displayName: 'テストチャネル1',
            membershipType: 'standard',
          },
          {
            id: 'channel2',
            displayName: 'テストチャネル2',
            membershipType: 'standard',
          },
        ],
      };

      const mockClient = {
        api: jest.fn().mockReturnThis(),
        top: jest.fn().mockReturnThis(),
        get: jest.fn()
          .mockResolvedValueOnce(mockTeamsResponse) // チーム取得
          .mockResolvedValue(mockChannelsResponse), // チャネル取得（各チーム用）
      };

      mockInitGraphClient.mockReturnValue(mockClient);

      const { result } = renderHook(() => useUserChatsAndChannelsAccessor(mockTokenProvider));

      let teamsResult: IPaginatedResult<IUserTeam> | undefined;
      await act(async () => {
        if (result.current.fetchUserTeamsAndChannelsPaginated) {
          teamsResult = await result.current.fetchUserTeamsAndChannelsPaginated();
        }
      });

      // 結果の検証
      expect(teamsResult).toBeDefined();
      expect(teamsResult!.items).toHaveLength(2);
      expect(teamsResult!.hasMore).toBe(true);
      expect(teamsResult!.nextPageToken).toBe('https://graph.microsoft.com/v1.0/me/joinedTeams?$skip=2');

      // 1件目のチーム
      expect(teamsResult!.items[0]).toEqual({
        id: 'team1',
        displayName: 'テストチーム1',
        channels: [
          {
            id: 'channel1',
            displayName: 'テストチャネル1',
            membershipType: 'standard',
          },
          {
            id: 'channel2',
            displayName: 'テストチャネル2',
            membershipType: 'standard',
          },
        ],
      });

      // APIが正しく呼ばれることを確認
      expect(mockClient.api).toHaveBeenCalledWith('/me/joinedTeams');
      // チーム取得では top() は使用されない
    });

    // 統合機能のテスト - チャットとチーム・チャネルの両方を取得
    it('チャットとチーム・チャネルを統合して取得する', async () => {
      const mockChatsResponse = {
        value: [
          {
            id: 'chat1',
            topic: 'テストチャット1',
            chatType: 'oneOnOne',
            members: [
              { displayName: '田中太郎', id: 'user1' },
              { displayName: '佐藤花子', id: 'user2' },
            ],
          },
        ],
        '@odata.nextLink': 'https://graph.microsoft.com/v1.0/me/chats?$skip=1',
      };

      const mockTeamsResponse = {
        value: [
          {
            id: 'team1',
            displayName: 'テストチーム1',
          },
        ],
        '@odata.nextLink': 'https://graph.microsoft.com/v1.0/me/joinedTeams?$skip=1',
      };

      const mockChannelsResponse = {
        value: [
          {
            id: 'channel1',
            displayName: 'テストチャネル1',
            membershipType: 'standard',
          },
        ],
      };

      const mockClient = {
        api: jest.fn().mockReturnThis(),
        expand: jest.fn().mockReturnThis(),
        orderby: jest.fn().mockReturnThis(),
        top: jest.fn().mockReturnThis(),
        get: jest.fn()
          .mockResolvedValueOnce(mockChatsResponse) // チャット取得
          .mockResolvedValueOnce(mockTeamsResponse) // チーム取得
          .mockResolvedValue(mockChannelsResponse), // チャネル取得
      };

      mockInitGraphClient.mockReturnValue(mockClient);

      const { result } = renderHook(() => useUserChatsAndChannelsAccessor(mockTokenProvider));

      let paginatedResult: IPaginatedResult<IUserChatItem> | undefined;
      await act(async () => {
        if (result.current.fetchUserChatsAndChannelsPaginated) {
          paginatedResult = await result.current.fetchUserChatsAndChannelsPaginated();
        }
      });

      // 結果の検証
      expect(paginatedResult).toBeDefined();
      expect(paginatedResult!.items).toHaveLength(2); // チャット1件 + チャネル1件
      expect(paginatedResult!.hasMore).toBe(true);

      // チャットアイテム
      expect(paginatedResult!.items[0]).toEqual({
        id: 'chat1',
        name: 'テストチャット1',
        type: 'チャット',
        chatType: 'oneOnOne',
      });

      // チャネルアイテム
      expect(paginatedResult!.items[1]).toEqual({
        id: 'channel1',
        name: 'テストチーム1 - テストチャネル1',
        type: 'チャネル',
        chatType: 'TeamsChannel',
        teamId: 'team1',
      });
    });
  });
});
