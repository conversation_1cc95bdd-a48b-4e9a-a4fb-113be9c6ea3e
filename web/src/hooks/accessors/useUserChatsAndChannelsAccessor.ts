import React, { useCallback } from 'react';
import { WeakTokenProvider } from '../../types/TokenProvider';
import {
  initGraphClient,
} from './useGraphApiAccessor';

// エラー定数
export const UseUserChatsAndChannelsError = {
  TOKEN_PROVIDER_NOT_AVAILABLE: 'TOKEN_PROVIDER_NOT_AVAILABLE',
  FETCH_CHATS_FAILED: 'FETCH_CHATS_FAILED',
  FETCH_TEAMS_FAILED: 'FETCH_TEAMS_FAILED',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
};

// GraphAPIから取得する値の型
// 表示に必要な型
export interface IUserChatItem {
  // chatId or ChannelId
  id: string;
  // 記事名
  name: string;
  // 内部処理で必要なtype(不要かも)
  type: 'チャット' | 'チャネル';
  // APIから帰ってくるchatType
  chatType: 'oneOnOne' | 'group' | 'meeting' |'TeamsChannel'; // チャットの場合はMicrosoft Graph APIのchatType、チャネルの場合は'TeamsChannel'
  // チャネルの場合のチームID(持っていて欲しい)
  teamId?: string;
}

export interface IUserChat {
  id: string;
  topic: string | null;
  chatType: string;
  members?: {
    displayName: string;
    id: string;
  }[];
}

export interface IUserTeam {
  id: string;
  displayName: string;
  channels?: IUserChannel[];
}

export interface IUserChannel {
  id: string;
  displayName: string;
  membershipType: string;
}

// ページネーション結果の型
export interface IPaginatedResult<T> {
  items: T[];
  hasMore: boolean;
  nextPageToken?: string;
  totalCount?: number;
}

// ページネーション対応の取得関数の型
export type FetchUserChatsAndChannelsPaginated = (
  pageToken?: string,
  pageSize?: number
) => Promise<IPaginatedResult<IUserChatItem>>;

// チーム・チャネル用ページネーション対応の取得関数の型
export type FetchUserTeamsAndChannelsPaginated = (
  pageToken?: string,
  pageSize?: number
) => Promise<IPaginatedResult<IUserTeam>>;

// 総件数取得関数の型
export type FetchUserChatsTotalCount = () => Promise<number>;

// チーム・チャネル総件数取得関数の型
export type FetchUserTeamsTotalCount = () => Promise<number>;

export type UseUserChatsAndChannelsReturnType = {
  fetchUserChatsAndChannelsPaginated: FetchUserChatsAndChannelsPaginated | undefined;
  fetchUserTeamsAndChannelsPaginated: FetchUserTeamsAndChannelsPaginated | undefined;
  fetchUserChatsTotalCount: FetchUserChatsTotalCount | undefined;
  fetchUserTeamsTotalCount: FetchUserTeamsTotalCount | undefined;
  isLoading: boolean;
  error: string | null;
}

/**
 * ユーザーが参加しているチャットの一覧を取得する（ページネーション対応）
 * @param tokenProvider トークンプロバイダー
 * @param pageToken 次のページを取得するためのトークン（初回はundefined）
 * @param pageSize 1ページあたりの件数（デフォルト: 20）
 * @returns ページネーション結果
 */
async function fetchUserChatsPaginatedImpl(
  tokenProvider: WeakTokenProvider,
  pageToken?: string,
  pageSize = 20,
): Promise<IPaginatedResult<IUserChat>> {
  const client = initGraphClient(tokenProvider);
  if (!client) {
    return Promise.reject(new Error(UseUserChatsAndChannelsError.TOKEN_PROVIDER_NOT_AVAILABLE));
  }
  try {
    let response;
    if (pageToken) {
      // 次のページを取得
      response = await client
        .api(pageToken)
        .get();
    } else {
      // 初回リクエスト
      response = await client
        .api('/me/chats')
        .expand('members')
        .orderby('lastMessagePreview/createdDateTime desc')
        .top(pageSize)
        .get();
    }
    const chats: IUserChat[] = response.value || [];
    const nextLink = response['@odata.nextLink'];
    return {
      items: chats,
      hasMore: !!nextLink,
      nextPageToken: nextLink,
    };
  } catch (error) {
    throw new Error(UseUserChatsAndChannelsError.FETCH_CHATS_FAILED);
  }
}

/**
 * ユーザーが参加しているチャットの総件数を取得する
 * @param tokenProvider トークンプロバイダー
 * @returns 総件数
 */
async function fetchUserChatsTotalCountImpl(
  tokenProvider: WeakTokenProvider,
): Promise<number> {
  const client = initGraphClient(tokenProvider);
  if (!client) {
    return Promise.reject(new Error(UseUserChatsAndChannelsError.TOKEN_PROVIDER_NOT_AVAILABLE));
  }

  try {
    // Microsoft Graph APIの/me/chatsエンドポイントは$countパラメータをサポートしていない
    // ページネーションを使用して全件を取得し、総件数をカウントする
    // IDのみを取得して効率化
    let totalCount = 0;
    let response = await client
      .api('/me/chats')
      .select('id')
      .top(50) // 最大値を使用して効率化
      .get();
    // 1ページ目の件数を追加
    totalCount += response.value?.length || 0;
    // 次ページがあれば繰り返す
    while (response['@odata.nextLink']) {
      // eslint-disable-next-line no-await-in-loop
      response = await client.api(response['@odata.nextLink']).get();
      totalCount += response.value?.length || 0;
    }
    return totalCount;
  } catch (error) {
    throw new Error(UseUserChatsAndChannelsError.FETCH_CHATS_FAILED);
  }
}
/**
 * ユーザーが参加しているチームとチャネルの一覧を取得する（ページネーション対応）
 * @param tokenProvider トークンプロバイダー
 * @param pageToken 次のページを取得するためのトークン（初回はundefined）
 * @param pageSize 1ページあたりの件数（デフォルト: 20）※/me/joinedTeamsエンドポイントは$topをサポートしていないため使用されません
 * @returns ページネーション結果
 */
async function fetchUserTeamsAndChannelsPaginatedImpl(
  tokenProvider: WeakTokenProvider,
  pageToken?: string,
): Promise<IPaginatedResult<IUserTeam>> {
  const client = initGraphClient(tokenProvider);
  if (!client) {
    return Promise.reject(new Error(UseUserChatsAndChannelsError.TOKEN_PROVIDER_NOT_AVAILABLE));
  }

  try {
    let teamsResponse;
    if (pageToken) {
      // 次のページを取得
      teamsResponse = await client
        .api(pageToken)
        .get();
    } else {
      // 初回リクエスト
      teamsResponse = await client
        .api('/me/joinedTeams')
        .get();
    }

    const teams: IUserTeam[] = teamsResponse.value || [];

    // 各チームのチャネルを取得
    const teamsWithChannels = await Promise.all(
      teams.map(async (team) => {
        try {
          const channelsResponse = await client
            .api(`/teams/${team.id}/channels`)
            .get();
          return { ...team, channels: channelsResponse.value || [] };
        } catch (error) {
          return { ...team, channels: [] };
        }
      }),
    );

    const nextLink = teamsResponse['@odata.nextLink'];
    return {
      items: teamsWithChannels,
      hasMore: !!nextLink,
      nextPageToken: nextLink,
    };
  } catch (error) {
    throw new Error(UseUserChatsAndChannelsError.FETCH_TEAMS_FAILED);
  }
}

/**
 * ユーザーが参加しているチーム・チャネルの総件数を取得する
 * @param tokenProvider トークンプロバイダー
 * @returns 総件数（全チームの全チャネル数）
 */
async function fetchUserTeamsAndChannelsTotalCountImpl(
  tokenProvider: WeakTokenProvider,
): Promise<number> {
  const client = initGraphClient(tokenProvider);
  if (!client) {
    return Promise.reject(new Error(UseUserChatsAndChannelsError.TOKEN_PROVIDER_NOT_AVAILABLE));
  }

  try {
    // Microsoft Graph APIの/me/joinedTeamsエンドポイントは$countパラメータをサポートしていない
    // ページネーションを使用して全チームを取得し、各チームのチャネル数をカウントする
    let totalChannelCount = 0;
    let teamsResponse = await client
      .api('/me/joinedTeams')
      .select('id') // IDのみを取得して効率化
      .get();

    // 各ページのチームを処理する関数
    const processTeamsPage = async (teams: IUserTeam[]): Promise<number> => {
      const channelCounts = await Promise.all(
        teams.map(async (team) => {
          try {
            const channelsResponse = await client
              .api(`/teams/${team.id}/channels`)
              .select('id') // IDのみを取得して効率化
              .get();
            return channelsResponse.value?.length || 0;
          } catch (error) {
            // チャネル取得に失敗した場合は0として扱う
            return 0;
          }
        }),
      );
      return channelCounts.reduce((sum, count) => sum + count, 0);
    };

    // 1ページ目のチームを処理
    const teams: IUserTeam[] = teamsResponse.value || [];
    totalChannelCount += await processTeamsPage(teams);

    // 次ページがあれば繰り返す
    while (teamsResponse['@odata.nextLink']) {
      // eslint-disable-next-line no-await-in-loop
      teamsResponse = await client.api(teamsResponse['@odata.nextLink']).get();
      const nextPageTeams: IUserTeam[] = teamsResponse.value || [];
      // eslint-disable-next-line no-await-in-loop
      totalChannelCount += await processTeamsPage(nextPageTeams);
    }

    return totalChannelCount;
  } catch (error) {
    throw new Error(UseUserChatsAndChannelsError.FETCH_TEAMS_FAILED);
  }
}

/**
 * チャットとチャネルを統合してIUserChatItem形式に変換する
 * chatTypeをチャネルとチャットで振り分け
 */
function convertToUserChatItems(
  // APIから取得したchatItems
  chatItems: IUserChat[],
  // APIから取得したteamsItems(teamsの下にchannelがある)
  teams: IUserTeam[],
): IUserChatItem[] {
  // 空のitemsを作成
  const items: IUserChatItem[] = [];
  // チャットを追加
  chatItems.forEach((chatItem) => {
    let name = chatItem.topic || 'チャット';

    // トピックがない場合はメンバー名を使用
    if (!chatItem.topic && chatItem.members && chatItem.members.length > 0) {
      const memberNames = chatItem.members
        .map((member) => member.displayName)
        .filter((memberName) => memberName)
        .join(', ');
      name = memberNames || 'チャット';
    }

    items.push({
      id: chatItem.id,
      name,
      type: 'チャット',
      chatType: chatItem.chatType as 'oneOnOne' | 'group' | 'meeting', // Microsoft Graph APIのchatTypeを保持
    });
  });

  // チャネルを追加
  teams.forEach((team) => {
    if (team.channels) {
      team.channels.forEach((channel) => {
        items.push({
          id: channel.id,
          name: `${team.displayName} - ${channel.displayName}`,
          type: 'チャネル',
          // チャネルのchatTypeはTeamsChannelで固定
          chatType: 'TeamsChannel',
          // チャネルの場合はteamIdを設定
          teamId: team.id,
        });
      });
    }
  });

  return items;
}

/**
 * ユーザーのチャットとチャネルを取得するカスタムフック
 */
const useUserChatsAndChannelsAccessor = (
  tokenProvider: WeakTokenProvider,
): UseUserChatsAndChannelsReturnType => {
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  const fetchUserChatsAndChannelsPaginated = React.useCallback(async (
    pageToken?: string,
    pageSize = 20,
  ): Promise<IPaginatedResult<IUserChatItem>> => {
    if (!tokenProvider) {
      throw new Error(UseUserChatsAndChannelsError.TOKEN_PROVIDER_NOT_AVAILABLE);
    }

    setIsLoading(true);
    setError(null);

    try {
      // チャットとチーム・チャネルを並行して取得（ページネーション対応）
      const [chatsResult, teamsResult] = await Promise.all([
        fetchUserChatsPaginatedImpl(tokenProvider, pageToken, pageSize),
        fetchUserTeamsAndChannelsPaginatedImpl(tokenProvider, pageToken),
      ]);

      // チャットとチーム・チャネルを統合してIUserChatItem形式に変換
      const items = convertToUserChatItems(chatsResult.items, teamsResult.items);

      setIsLoading(false);
      return {
        items,
        hasMore: chatsResult.hasMore || teamsResult.hasMore,
        nextPageToken: chatsResult.nextPageToken || teamsResult.nextPageToken,
        totalCount: (chatsResult.totalCount || 0) + (teamsResult.totalCount || 0),
      };
    } catch (err) {
      const errorMessage = err instanceof Error
        ? err.message
        : UseUserChatsAndChannelsError.UNKNOWN_ERROR;
      setError(errorMessage);
      setIsLoading(false);
      throw new Error(errorMessage);
    }
  }, [tokenProvider]);

  const fetchUserChatsTotalCount = React.useCallback(async (): Promise<number> => {
    if (!tokenProvider) {
      throw new Error(UseUserChatsAndChannelsError.TOKEN_PROVIDER_NOT_AVAILABLE);
    }
    try {
      return await fetchUserChatsTotalCountImpl(tokenProvider);
    } catch (err) {
      const errorMessage = err instanceof Error
        ? err.message
        : UseUserChatsAndChannelsError.UNKNOWN_ERROR;
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [tokenProvider]);

  const fetchUserTeamsTotalCount = React.useCallback(async (): Promise<number> => {
    if (!tokenProvider) {
      throw new Error(UseUserChatsAndChannelsError.TOKEN_PROVIDER_NOT_AVAILABLE);
    }
    try {
      return await fetchUserTeamsAndChannelsTotalCountImpl(tokenProvider);
    } catch (err) {
      const errorMessage = err instanceof Error
        ? err.message
        : UseUserChatsAndChannelsError.UNKNOWN_ERROR;
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [tokenProvider]);

  // チーム・チャネルのページネーション取得関数
  const fetchUserTeamsAndChannelsPaginated = useCallback(async (
    pageToken?: string,
    _pageSize?: number, // チーム・チャネル取得では使用されない
  ): Promise<IPaginatedResult<IUserTeam>> => {
    if (!tokenProvider) {
      throw new Error(UseUserChatsAndChannelsError.TOKEN_PROVIDER_NOT_AVAILABLE);
    }
    return fetchUserTeamsAndChannelsPaginatedImpl(tokenProvider, pageToken);
  }, [tokenProvider]);

  return {
    fetchUserChatsAndChannelsPaginated: tokenProvider
      ? fetchUserChatsAndChannelsPaginated
      : undefined,
    fetchUserTeamsAndChannelsPaginated: tokenProvider
      ? fetchUserTeamsAndChannelsPaginated
      : undefined,
    fetchUserChatsTotalCount: tokenProvider ? fetchUserChatsTotalCount : undefined,
    fetchUserTeamsTotalCount: tokenProvider ? fetchUserTeamsTotalCount : undefined,
    isLoading,
    error,
  };
};

export default useUserChatsAndChannelsAccessor;
